import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { z } from 'zod';

const querySchema = z.object({
  categoryId: z.string().uuid(),
});

type CategoryTrendData = {
  month: string;
  total_spent: number;
};

export async function GET(request: Request) {
  const supabase = await createSupabaseServerClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const queryParams = Object.fromEntries(searchParams.entries());

  const validation = querySchema.safeParse(queryParams);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid query parameters', details: validation.error.flatten() },
      { status: 400 }
    );
  }

  const { categoryId } = validation.data;

  try {
    // First, get category name for the metadata
    const { data: categoryData, error: categoryError } = await supabase
      .from('user_categories')
      .select('name')
      .eq('id', categoryId)
      .eq('user_id', user.id)
      .single();

    if (categoryError || !categoryData) {
      return NextResponse.json({ error: 'Category not found' }, { status: 404 });
    }

    const { data, error } = await supabase.rpc('get_category_trend', {
      p_user_id: user.id,
      p_category_id: categoryId,
    });

    if (error) {
      console.error('Error calling get_category_trend:', error);
      return NextResponse.json({ error: 'Failed to fetch category trend data' }, { status: 500 });
    }

    const formattedData = data.map((item: CategoryTrendData) => ({
      month: item.month,
      totalSpent: item.total_spent,
    }));

    return NextResponse.json({
      data: formattedData,
      metadata: {
        categoryId,
        categoryName: categoryData.name,
        currency: 'USD', // Assuming USD for now
      },
    });
  } catch (error) {
    console.error('[CATEGORY_TREND_GET_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
