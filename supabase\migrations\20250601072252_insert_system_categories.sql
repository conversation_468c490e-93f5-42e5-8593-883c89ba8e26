-- Insert default system transaction categories
-- This migration contains DML statements that cannot be handled by declarative schema

INSERT INTO transaction_categories (id, name, description, category_type, icon, color) VALUES
    (uuid_generate_v4(), 'Food & Dining', 'Restaurants, groceries, and food delivery', 'system', '🍽️', '#FF6B6B'),
    (uuid_generate_v4(), 'Transportation', 'Gas, public transit, rideshare, and vehicle expenses', 'system', '🚗', '#4ECDC4'),
    (uuid_generate_v4(), 'Shopping', 'Retail purchases, clothing, and general merchandise', 'system', '🛍️', '#45B7D1'),
    (uuid_generate_v4(), 'Entertainment', 'Movies, concerts, games, and recreational activities', 'system', '🎬', '#96CEB4'),
    (uuid_generate_v4(), 'Bills & Utilities', 'Rent, electricity, water, internet, and phone bills', 'system', '📄', '#FFEAA7'),
    (uuid_generate_v4(), 'Healthcare', 'Medical expenses, pharmacy, and health insurance', 'system', '🏥', '#DDA0DD'),
    (uuid_generate_v4(), 'Education', 'Tuition, books, courses, and educational materials', 'system', '📚', '#98D8C8'),
    (uuid_generate_v4(), 'Travel', 'Hotels, flights, and vacation expenses', 'system', '✈️', '#F7DC6F'),
    (uuid_generate_v4(), 'Income', 'Salary, freelance, and other income sources', 'system', '💰', '#82E0AA'),
    (uuid_generate_v4(), 'Investments', 'Stocks, bonds, and investment transfers', 'system', '📈', '#85C1E9'),
    (uuid_generate_v4(), 'Other', 'Miscellaneous and uncategorized transactions', 'system', '📦', '#D5DBDB');;
