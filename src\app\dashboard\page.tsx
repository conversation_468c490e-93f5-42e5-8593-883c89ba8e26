'use client';

import { useAuth } from '@/components/auth/AuthProvider';
import PlaidLink from '@/components/plaid/PlaidLink';
import Link from 'next/link';
import AccountsList from '@/components/plaid/AccountsList';
import { useProfile } from '@/lib/hooks/useProfile';
import OnboardingGuide from '@/components/auth/OnboardingGuide';

function DashboardPage() {
  const { user } = useAuth();
  const { profile, refetch } = useProfile(user?.id);

  return (
    <div className='flex flex-col items-center justify-center min-h-full p-8'>
      <h1 className='text-3xl font-bold mb-4'>Dashboard</h1>
      {profile?.onboarding_completed ? (
        <>
          <PlaidLink userId={user!.id} />
          <div className='mt-6 w-full flex justify-center'>
            <AccountsList />
          </div>
        </>
      ) : (
        <OnboardingGuide refetch={refetch} />
      )}
      <Link href='/profile' className='mt-4 text-blue-600 hover:underline'>
        Go to Profile
      </Link>
    </div>
  );
}

export default DashboardPage;
