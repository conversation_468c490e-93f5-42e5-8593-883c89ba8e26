import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { z } from 'zod';

const querySchema = z.object({
  period: z.enum(['this_month', 'last_month', 'last_90_days']),
});

type CategorySpending = {
  category_id: string;
  category_name: string;
  total_spent: number;
};

export async function GET(request: Request) {
  const supabase = await createSupabaseServerClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const queryParams = Object.fromEntries(searchParams.entries());

  const validation = querySchema.safeParse(queryParams);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid query parameters', details: validation.error.flatten() },
      { status: 400 }
    );
  }

  const { period } = validation.data;

  let startDate: string;
  let endDate: string = new Date().toISOString().split('T')[0]; // Today

  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();

  switch (period) {
    case 'this_month':
      startDate = new Date(year, month, 1).toISOString().split('T')[0];
      break;
    case 'last_month':
      startDate = new Date(year, month - 1, 1).toISOString().split('T')[0];
      endDate = new Date(year, month, 0).toISOString().split('T')[0];
      break;
    case 'last_90_days':
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(now.getDate() - 90);
      startDate = ninetyDaysAgo.toISOString().split('T')[0];
      break;
  }

  try {
    const { data, error } = await supabase.rpc('get_category_spending', {
      p_user_id: user.id,
      p_start_date: startDate,
      p_end_date: endDate,
    });

    if (error) {
      console.error('Error calling get_category_spending:', error);
      return NextResponse.json({ error: 'Failed to fetch spending breakdown' }, { status: 500 });
    }

    const formattedData = data.map((item: CategorySpending) => ({
      categoryName: item.category_name,
      totalSpent: item.total_spent,
    }));

    return NextResponse.json({
      data: formattedData,
      metadata: {
        period,
        startDate,
        endDate,
        currency: 'USD', // Assuming USD for now
      },
    });
  } catch (error) {
    console.error('[SPENDING_BREAKDOWN_GET_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
