-- Update the handle_new_user function to assign default categories
create or replace function public.handle_new_user()
returns trigger
language plpgsql
security definer set search_path = public
as $$
begin
  -- Create user profile
  insert into public.profiles (id, email)
  values (new.id, new.email);
  
  -- Copy system categories to user_categories for the new user
  insert into public.user_categories (user_id, name, description, base_category_id, icon, color, is_active)
  select 
    new.id as user_id,
    tc.name,
    tc.description,
    tc.id as base_category_id,
    tc.icon,
    tc.color,
    tc.is_active
  from public.transaction_categories tc
  where tc.category_type = 'system' and tc.is_active = true;
  
  return new;
end;
$$;

-- Recreate the trigger to ensure it uses the updated function
create or replace trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();;
