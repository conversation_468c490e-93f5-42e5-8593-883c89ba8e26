## Quick Start

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/your-repo/navsync.git
    cd navsync
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Copy the `.env.example` file to `.env.local` and fill in the required values. See the "Environment Variables" section for details.

    ```bash
    cp .env.example .env.local
    ```

4.  **Run the development server:**
    ```bash
    npm run dev
    ```

## Environment Variables

To run the application, you will need to create a `.env.local` file in the root of the project and add the following environment variables. You can copy the example file as a starting point: `cp .env.example .env.local`.

| Variable                        | Description                                                            | Example                                |
| ------------------------------- | ---------------------------------------------------------------------- | -------------------------------------- |
| `NEXT_PUBLIC_SUPABASE_URL`      | The URL for your Supabase project.                                     | `https://your-project-ref.supabase.co` |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | The anonymous key for your Supabase project.                           | `your-anon-key`                        |
| `SUPABASE_SERVICE_ROLE_KEY`     | The service role key for your Supabase project (used for admin tasks). | `your-service-role-key`                |
| `PLAID_CLIENT_ID`               | Your Plaid API client ID.                                              | `your-plaid-client-id`                 |
| `PLAID_SECRET`                  | Your Plaid API secret for the Sandbox environment.                     | `your-plaid-secret`                    |
| `PLAID_ENV`                     | The Plaid environment to use.                                          | `sandbox`                              |

## Technology Stack

This project is built with the following core technologies:

- **Next.js:** A React framework for building full-stack web applications.
- **React:** A JavaScript library for building user interfaces.
- **TypeScript:** A typed superset of JavaScript that compiles to plain JavaScript.
- **Tailwind CSS:** A utility-first CSS framework for rapid UI development.
- **Supabase:** An open-source Firebase alternative for the database, authentication, and storage.
- **Plaid:** A platform for connecting to users' bank accounts.
- **Zod:** A TypeScript-first schema declaration and validation library.
- **React Hook Form:** A library for flexible and extensible forms with easy-to-use validation.
- **Jest & React Testing Library:** For unit and component testing.

## Database

This project uses Supabase for its PostgreSQL database, authentication, and storage. All database schema changes are managed through migrations using the Supabase CLI.

### Working with Migrations

This project uses the Supabase CLI to manage database schema changes through migration files. This keeps your schema in version control.

1.  **Link your local project to your Supabase project** (only needs to be done once):

    ```bash
    npm run supabase -- link --project-ref &lt;your-project-id&gt;
    ```

    You will be prompted for your database password.

2.  **Pull the initial schema** from your remote database. This creates the first migration file which acts as a baseline.

    ```bash
    npm run supabase -- db pull
    ```

    Commit the new `supabase` directory to your repository.

3.  **Make schema changes** in the Supabase Studio (e.g., creating a table or adding a column).

4.  **Generate a new migration file** from your remote changes:

    ```bash
    npm run supabase -- db diff -f &lt;migration_name&gt;
    ```

    Replace `&lt;migration_name&gt;` with a descriptive name for your migration (e.g., `add_user_profiles_table`). This compares your linked remote database with your local migration files and creates a new SQL file with the changes.

5.  **Apply migrations to other environments:** When another developer (or a CI/CD pipeline) needs to apply new migrations to a different database, they can run:
    ```bash
    npm run supabase -- db push
    ```

# NAVsync.io Development Guidelines

This document outlines the development practices, conventions, and structure for the NAVsync.io project. Adhering to these guidelines will help maintain code quality, consistency, and ease of collaboration.

## 1. Recommended Folder Structure

The project follows a structure conventional for Next.js applications, with some specific additions for NAVsync.io.

- **/public**: Static assets accessible directly via URL (e.g., images, favicons).
- **/src**: Main application source code.
  - **/app**: Next.js App Router directory. Contains route handlers, pages, layouts, and API routes.
    - **/(app)**: Group for authenticated application routes (e.g., dashboard, transactions).
    - **/(auth)**: Group for authentication-related routes (e.g., login, signup).
    - **/api**: API route handlers.
    - **layout.tsx**: Root layout.
    - **page.tsx**: Root page (landing page).
    - **globals.css**: Global styles.
  - **/components**: Reusable UI components.
    - **/ui**: Components from shadcn/ui (or custom low-level UI elements).
    - **/common**: General-purpose components used across multiple features.
    - **/feature**: Feature-specific components (e.g., `budgeting`, `transactions`).
  - **/lib**: Utility functions, helper modules, and third-party service integrations.
    - **`utils.ts`**: General utility functions.
    - **`supabase/client.ts`** and **`supabase/server.ts`**: Supabase client and server configurations and helper functions.
    - **`plaid.ts`**: Plaid client configuration and helper functions.
    - **`dates.ts`**: Date manipulation utilities (e.g., using `date-fns`).
  - **/hooks**: Custom React hooks.
  - **/contexts**: React Context API providers.
  - **/types**: TypeScript type definitions and interfaces, especially shared types.
  - **/env.mjs**: Environment variable validation using Zod.
  - **/styles**: Global styles or theme configurations if not covered by `globals.css` or Tailwind.
- **/.github**: GitHub-specific files.
  - **/workflows**: GitHub Actions CI/CD workflows (e.g., `ci.yml`).
- **/.husky**: Husky pre-commit hook configurations.
- **/Memory**: Project planning and documentation files (like this one, PRD, dev plans).
- **/.vscode**: VS Code editor specific settings (e.g., `settings.json`, `extensions.json`).
- **Other root files**: `next.config.ts`, `tailwind.config.ts`, `postcss.config.mjs`, `tsconfig.json`, `package.json`, `.nvmrc`, `.gitignore`, `.prettierrc.json`, `eslint.config.mjs`, `README.md`, `DEVELOPMENT.md`.

## 2. Naming Conventions

Consistency in naming improves readability and maintainability.

- **Files and Folders:**
  - Use `kebab-case` (e.g., `user-profile.tsx`, `auth-routes`).
  - Exception: Next.js specific files like `page.tsx`, `layout.tsx`, `route.ts`.
- **Components:**
  - Use `PascalCase` for component files and named exports (e.g., `UserProfile.tsx`, `export function UserProfile(...)`).
- **Variables and Functions:**
  - Use `camelCase` (e.g., `const userName = ...`, `function getUserDetails()`).
- **Types and Interfaces:**
  - Use `PascalCase` (e.g., `interface UserProfile { ... }`, `type AuthStatus = ...`).
- **CSS Classes (Tailwind):**
  - Follow Tailwind CSS utility-first conventions. For custom components, BEM-like naming can be considered if creating more complex CSS modules, but prefer Tailwind utilities.
- **API Routes:**
  - Use `kebab-case` for folder/file names (e.g., `/app/api/user-profile/route.ts`).
  - Endpoint paths should also be `kebab-case` (e.g., `/api/user-profile`).

## 3. Code Organization Principles

- **Modularity:** Break down complex features into smaller, manageable modules and components.
- **Single Responsibility Principle (SRP):** Components and functions should ideally do one thing well.
- **Separation of Concerns:**
  - Keep UI logic (components) separate from business logic (services, hooks).
  - Separate API route handlers from core business logic.
  - Data fetching logic can be co-located with components (Next.js App Router server components) or centralized in hooks/services for client components.
- **Reusability:** Create generic components and utility functions whenever possible.
- **Clarity over Brevity:** Write code that is easy to understand, even if it's slightly more verbose. Add comments for complex logic.
- **Type Safety:** Leverage TypeScript extensively. Define clear types and interfaces for props, state, API responses, and function signatures. Avoid `any` where possible.

## 4. Database Schema Location and Migration Process

- **Schema Definition:** The primary database schema will be defined and managed through Supabase.
  - Initial schema design will be documented (e.g., in an ERD or markdown file within `/Memory` or a dedicated `/supabase/migrations` folder once we start).
  - Supabase Studio (dashboard) will be used for initial table creation and modifications during early development.
- **Migrations:**
  - For schema changes, Supabase supports SQL migrations.
  - We will use the Supabase CLI to manage migrations.
  - Migrations will be stored in the `/supabase/migrations` directory in the repository (this directory will be created when we initialize Supabase local development).
  - Each migration file will represent a set of schema changes (up and down).
  - **Process (to be refined):**
    1.  Develop schema changes locally (e.g., using Supabase Studio with local Supabase instance or by writing SQL).
    2.  Generate migration files using `supabase db diff` or by writing them manually.
    3.  Test migrations locally.
    4.  Apply migrations to staging/production environments using the Supabase CLI or dashboard.
- **Row Level Security (RLS):** RLS policies are critical for data security and will be defined directly in Supabase, ideally as part of the SQL schema migrations.

## 5. Testing

### Running Tests

To run the full test suite, use the following command:

```bash
npm test
```

To run the tests in watch mode, which is useful during development, use:

```bash
npm test -- --watch
```

- **Unit Tests:**
  - Focus: Test individual functions, hooks, and utility modules.
  - Framework: Jest (or Vitest, to be decided).
  - Location: `*.test.ts` or `*.spec.ts` files co-located with the source files (e.g., `src/lib/utils.test.ts`).
- **Component Tests:**
  - Focus: Test individual React components in isolation, verifying rendering and interaction.
  - Framework: React Testing Library with Jest/Vitest.
  - Location: `*.test.tsx` or `*.spec.tsx` files co-located with component files (e.g., `src/components/common/Button.test.tsx`).
- **Integration Tests:**
  - Focus: Test interactions between multiple components or modules, or simple user flows.
  - Framework: React Testing Library, potentially with MSW (Mock Service Worker) for API mocking.
  - Location: Can be in a top-level `__tests__` folder or co-located with feature modules.
- **End-to-End (E2E) Tests:**
  - Focus: Test complete user flows through the application in a browser-like environment.
  - Framework: Playwright or Cypress (to be decided).
  - Location: Typically in a separate `/tests` or `/e2e` directory at the project root.
- **CI Integration:** All tests (unit, component, integration) will be run as part of the CI/CD pipeline (GitHub Actions) on every push and pull request. E2E tests might run on a less frequent schedule or against specific environments.
- **Coverage:** Aim for meaningful test coverage, prioritizing critical paths and complex logic.

## 6. Deployment Process Overview

- **Hosting Platform:** Vercel (primary choice for Next.js applications).
- **Environments:**
  - **Development:** Local machine (`npm run dev`).
  - **Preview/Staging:** Vercel automatically creates preview deployments for each pull request and branch push. A dedicated `staging` branch can be used for a more stable pre-production environment.
  - **Production:** The `main` branch will be deployed to production on Vercel.
- **Deployment Trigger:**
  - Pushing to the `main` branch triggers a production deployment.
  - Pushing to other branches or creating pull requests triggers preview deployments.
- **CI/CD Pipeline (GitHub Actions):**
  - The `ci.yml` workflow runs linters, type checks, tests, and a production build on pushes/PRs to `main`.
  - This ensures code quality before deployment.
- **Environment Variables:**
  - Production and preview/staging environment variables will be configured directly in the Vercel project settings.
  - The [`src/env.mjs`](src/env.mjs) validation will ensure these are correctly set in deployed environments.
- **Rollbacks:** Vercel provides mechanisms for instant rollbacks to previous deployments if issues arise.

---

This document is a living guide and will be updated as the project evolves.
