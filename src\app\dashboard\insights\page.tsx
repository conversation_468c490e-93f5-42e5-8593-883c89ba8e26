'use client';

import React from 'react';
import CategoryBreakdownChart from '@/components/insights/CategoryBreakdownChart';
import BudgetComparisonTable from '@/components/insights/BudgetComparisonTable';
import { CategoryTrendChart } from '@/components/insights/CategoryTrendChart';

const InsightsPage = () => {
  return (
    <div className='min-h-screen bg-background'>
      <div className='container mx-auto px-4 py-8 max-w-7xl'>
        {/* Page Header */}
        <div className='mb-8'>
          <h1 className='text-4xl font-bold tracking-tight mb-3'>Financial Insights</h1>
          <p className='text-lg text-muted-foreground max-w-2xl'>
            Analyze your spending patterns, track budget performance, and discover financial trends
            to make informed decisions.
          </p>
        </div>

        {/* Main Dashboard Grid */}
        <div className='grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8'>
          {/* Spending Breakdown Chart */}
          <div className='xl:col-span-1'>
            <CategoryBreakdownChart />
          </div>

          {/* Budget Comparison Table */}
          <div className='xl:col-span-1'>
            <BudgetComparisonTable />
          </div>
        </div>

        {/* Category Trend Chart - Full Width */}
        <div className='w-full'>
          <CategoryTrendChart />
        </div>
      </div>
    </div>
  );
};

export default InsightsPage;
