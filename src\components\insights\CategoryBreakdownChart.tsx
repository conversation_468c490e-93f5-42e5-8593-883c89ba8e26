'use client';

import { useState, useEffect } from 'react';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, ChartData } from 'chart.js';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

ChartJS.register(ArcElement, Tooltip, Legend);

type Period = 'this_month' | 'last_month' | 'last_90_days';

interface BreakdownData {
  categoryName: string;
  totalSpent: number;
}

interface SpendingBreakdownResponse {
  data: BreakdownData[];
  metadata: {
    period: string;
    startDate: string;
    endDate: string;
    currency: string;
  };
}

// Predefined colors for categories
const CHART_COLORS = [
  '#3b82f6', // blue
  '#ef4444', // red
  '#10b981', // green
  '#f59e0b', // yellow
  '#8b5cf6', // purple
  '#06b6d4', // cyan
  '#f97316', // orange
  '#84cc16', // lime
  '#ec4899', // pink
  '#6b7280', // gray
];

const CategoryBreakdownChart = () => {
  const [data, setData] = useState<ChartData<'doughnut'> | null>(null);
  const [period, setPeriod] = useState<Period>('this_month');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/insights/spending-breakdown?period=${period}`);
        if (!response.ok) {
          throw new Error('Failed to fetch spending breakdown');
        }
        const result: SpendingBreakdownResponse = await response.json();
        const breakdownData = result.data || [];

        if (breakdownData.length === 0) {
          setData(null); // No data to display
        } else {
          const chartData: ChartData<'doughnut'> = {
            labels: breakdownData.map((item) => item.categoryName),
            datasets: [
              {
                label: 'Spending',
                data: breakdownData.map((item) => item.totalSpent),
                backgroundColor: breakdownData.map(
                  (_, index) => CHART_COLORS[index % CHART_COLORS.length]
                ),
                borderColor: 'hsl(var(--background))',
                borderWidth: 2,
              },
            ],
          };
          setData(chartData);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [period]);

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle>Spending Breakdown</CardTitle>
        <Select onValueChange={(value: Period) => setPeriod(value)} defaultValue={period}>
          <SelectTrigger className='w-[180px]'>
            <SelectValue placeholder='Select period' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='this_month'>This Month</SelectItem>
            <SelectItem value='last_month'>Last Month</SelectItem>
            <SelectItem value='last_90_days'>Last 90 Days</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className='flex justify-center items-center h-64'>
            <LoadingSpinner />
          </div>
        ) : error ? (
          <div className='flex justify-center items-center h-64 text-red-500'>{error}</div>
        ) : data ? (
          <div className='relative h-64'>
            <Doughnut
              data={data}
              options={{
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: {
                      padding: 20,
                      usePointStyle: true,
                      color: 'hsl(var(--foreground))',
                      font: {
                        size: 12,
                      },
                    },
                  },
                  tooltip: {
                    backgroundColor: 'hsl(var(--popover))',
                    titleColor: 'hsl(var(--popover-foreground))',
                    bodyColor: 'hsl(var(--popover-foreground))',
                    borderColor: 'hsl(var(--border))',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                      label: function (context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce(
                          (a: number, b: number) => a + b,
                          0
                        );
                        const percentage = ((value / total) * 100).toFixed(1);
                        return `${label}: $${value.toFixed(2)} (${percentage}%)`;
                      },
                    },
                  },
                },
                cutout: '60%',
                elements: {
                  arc: {
                    borderWidth: 2,
                    borderColor: 'hsl(var(--background))',
                  },
                },
              }}
            />
          </div>
        ) : (
          <div className='flex justify-center items-center h-64'>
            <p>No spending data available for this period.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CategoryBreakdownChart;
