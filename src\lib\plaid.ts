import { Configuration, PlaidApi, PlaidEnvironments } from 'plaid';
import { env } from '../env.mjs';

const PLAID_CLIENT_ID = env.PLAID_CLIENT_ID;
const PLAID_SECRET = env.PLAID_SECRET_SANDBOX;

if (!PLAID_CLIENT_ID || !PLAID_SECRET) {
  throw new Error(
    'CRITICAL: Missing Plaid environment variables. Check PLAID_CLIENT_ID and PLAID_SECRET_SANDBOX.'
  );
}

const configuration = new Configuration({
  basePath: PlaidEnvironments.sandbox,
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': PLAID_CLIENT_ID,
      'PLAID-SECRET': PLAID_SECRET,
    },
  },
});

export const plaidClient = new PlaidApi(configuration);
