-- 12_category_rules.sql
CREATE TABLE category_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    rule_type TEXT NOT NULL, -- 'merchant_name', 'keyword'
    match_criteria TEXT NOT NULL,
    category_id UUID NOT NULL REFERENCES transaction_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

ALTER TABLE category_rules ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own category rules"
ON category_rules FOR ALL
USING (auth.uid() = user_id);

CREATE INDEX idx_category_rules_user_id ON category_rules(user_id);;
