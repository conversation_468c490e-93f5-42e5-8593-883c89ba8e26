CREATE OR REPLACE FUNCTION handle_transaction_sync(
    p_user_id UUID,
    p_account_id UUID,
    p_added_transactions JSONB,
    p_modified_transactions JSONB
)
RETURNS JSONB AS $$
DECLARE
    added_tx JSONB;
    modified_tx JSONB;
    existing_tx RECORD;
    new_tx_id UUID;
    log_entries JSONB[] := '{}';
    changes JSONB;
    change_key TEXT;
    old_value TEXT;
    new_value TEXT;
    result_summary JSONB;
    added_count INT := 0;
    modified_count INT := 0;
    v_category_id UUID;
BEGIN
    -- Process added transactions
    FOR added_tx IN SELECT * FROM jsonb_array_elements(p_added_transactions)
    LOOP
        -- Find matching category rule for auto-categorization
        v_category_id := NULL;
        
        SELECT category_id INTO v_category_id
        FROM category_rules
        WHERE user_id = p_user_id
          AND (
            (rule_type = 'merchant_name' AND LOWER(match_criteria) = LOWER(added_tx->>'merchant_name'))
            OR
            (rule_type = 'keyword' AND LOWER(added_tx->>'name') LIKE '%' || LOWER(match_criteria) || '%')
          )
        ORDER BY created_at DESC
        LIMIT 1;
        
        INSERT INTO transactions (
            user_id,
            account_id,
            plaid_transaction_id,
            amount,
            currency_code,
            transaction_date,
            authorized_date,
            posted_date,
            merchant_name,
            description,
            plaid_category,
            plaid_category_detailed,
            account_owner,
            transaction_type,
            transaction_code,
            location,
            is_recurring,
            status,
            is_pending,
            plaid_pending_transaction_id,
            plaid_metadata,
            category_id
        )
        VALUES (
            p_user_id,
            p_account_id,
            added_tx->>'transaction_id',
            (added_tx->>'amount')::DECIMAL,
            added_tx->>'iso_currency_code',
            (added_tx->>'date')::DATE,
            COALESCE((added_tx->>'authorized_date')::DATE, (added_tx->>'date')::DATE),
            (added_tx->>'date')::DATE,
            COALESCE(added_tx->>'merchant_name', 'N/A'),
            added_tx->>'name',
            COALESCE(added_tx->'personal_finance_category'->>'primary', 'UNCATEGORIZED'),
            COALESCE(added_tx->'personal_finance_category'->>'detailed', 'UNCATEGORIZED'),
            COALESCE(added_tx->>'account_owner', 'N/A'),
            added_tx->>'transaction_type',
            COALESCE(added_tx->>'transaction_code', 'N/A'),
            COALESCE(added_tx->'location', '{}'::jsonb),
            COALESCE((added_tx->>'is_recurring')::BOOLEAN, FALSE),
            'posted',
            (added_tx->>'pending')::BOOLEAN,
            added_tx->>'pending_transaction_id',
            added_tx,
            v_category_id
        )
        RETURNING id INTO new_tx_id;

        INSERT INTO transaction_audit_log (transaction_id, user_id, changed_field, new_value, change_source)
        VALUES (new_tx_id, p_user_id, 'transaction_created', new_tx_id::TEXT, 'plaid_sync');

        added_count := added_count + 1;
    END LOOP;

    -- Process modified transactions
    FOR modified_tx IN SELECT * FROM jsonb_array_elements(p_modified_transactions)
    LOOP
        -- Handle pending-to-posted transitions
        IF modified_tx->>'pending_transaction_id' IS NOT NULL THEN
            SELECT * INTO existing_tx
            FROM transactions
            WHERE plaid_pending_transaction_id = modified_tx->>'pending_transaction_id'
              AND user_id = p_user_id;
        ELSE
            SELECT * INTO existing_tx
            FROM transactions
            WHERE plaid_transaction_id = modified_tx->>'transaction_id'
              AND user_id = p_user_id;
        END IF;

        IF FOUND THEN
            changes := '{}'::JSONB;

            -- Compare and log changes for relevant fields
            IF existing_tx.amount::TEXT != (modified_tx->>'amount')::DECIMAL::TEXT THEN
                changes := jsonb_set(changes, '{amount}', jsonb_build_object('old', existing_tx.amount, 'new', (modified_tx->>'amount')::DECIMAL));
            END IF;
            IF existing_tx.merchant_name IS DISTINCT FROM (modified_tx->>'merchant_name') THEN
                changes := jsonb_set(changes, '{merchant_name}', jsonb_build_object('old', existing_tx.merchant_name, 'new', modified_tx->>'merchant_name'));
            END IF;
            IF existing_tx.description IS DISTINCT FROM (modified_tx->>'name') THEN
                changes := jsonb_set(changes, '{description}', jsonb_build_object('old', existing_tx.description, 'new', modified_tx->>'name'));
            END IF;
            IF existing_tx.is_pending IS DISTINCT FROM (modified_tx->>'pending')::BOOLEAN THEN
                 changes := jsonb_set(changes, '{is_pending}', jsonb_build_object('old', existing_tx.is_pending, 'new', (modified_tx->>'pending')::BOOLEAN));
            END IF;
            IF existing_tx.plaid_transaction_id IS DISTINCT FROM (modified_tx->>'transaction_id') THEN
                 changes := jsonb_set(changes, '{plaid_transaction_id}', jsonb_build_object('old', existing_tx.plaid_transaction_id, 'new', modified_tx->>'transaction_id'));
            END IF;


            -- Update the transaction record
            UPDATE transactions
            SET
                amount = (modified_tx->>'amount')::DECIMAL,
                currency_code = modified_tx->>'iso_currency_code',
                transaction_date = (modified_tx->>'date')::DATE,
                authorized_date = (modified_tx->>'authorized_date')::DATE,
                posted_date = (modified_tx->>'date')::DATE,
                merchant_name = modified_tx->>'merchant_name',
                description = modified_tx->>'name',
                plaid_category = modified_tx->'personal_finance_category'->'primary',
                plaid_category_detailed = modified_tx->'personal_finance_category'->>'detailed',
                transaction_type = modified_tx->>'transaction_type',
                location = modified_tx->'location',
                is_pending = (modified_tx->>'pending')::BOOLEAN,
                plaid_transaction_id = COALESCE(modified_tx->>'transaction_id', existing_tx.plaid_transaction_id),
                plaid_metadata = existing_tx.plaid_metadata || modified_tx,
                updated_at = NOW()
            WHERE id = existing_tx.id;

            -- Insert audit log entries for each change
            FOR change_key, old_value, new_value IN SELECT k, v->>'old', v->>'new' FROM jsonb_each(changes) AS t(k, v)
            LOOP
                INSERT INTO transaction_audit_log (transaction_id, user_id, changed_field, old_value, new_value, change_source)
                VALUES (existing_tx.id, p_user_id, change_key, old_value, new_value, 'plaid_sync');
            END LOOP;

            modified_count := modified_count + 1;
        END IF;
    END LOOP;

    result_summary := jsonb_build_object(
        'added', added_count,
        'modified', modified_count
    );

    RETURN result_summary;
END;
$$ LANGUAGE plpgsql;;
