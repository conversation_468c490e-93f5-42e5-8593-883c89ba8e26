'use client';

import { useState } from 'react';
import { Budget } from '@/lib/services/budgetService';

export interface UseBudgetsReturn {
  budget: Budget | null;
  isLoading: boolean;
  error: string | null;
  fetchBudget: (month: string) => Promise<void>;
  clearError: () => void;
}

export function useBudgets(): UseBudgetsReturn {
  const [budget, setBudget] = useState<Budget | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchBudget = async (month: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/budgets?month=${month}`);

      if (response.status === 404) {
        setBudget(null);
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to fetch budget');
      }

      const budgetData = await response.json();
      setBudget(budgetData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch budget');
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    budget,
    isLoading,
    error,
    fetchBudget,
    clearError,
  };
}
