'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import CategorySelector from '@/components/categories/CategorySelector';

interface Category {
  id: string;
  name: string;
}

interface InlineCategorySelectorProps {
  transactionId: string;
  currentCategoryId: string;
  categories: Category[];
  onOptimisticUpdate?: (transactionId: string, categoryId: string) => void;
}

/**
 * InlineCategorySelector provides inline category editing with optimistic updates.
 * It immediately updates the UI and reverts on API failure.
 */
export default function InlineCategorySelector({
  transactionId,
  currentCategoryId,
  categories,
  onOptimisticUpdate,
}: InlineCategorySelectorProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [originalCategoryId] = useState(currentCategoryId);

  const handleCategoryChange = async (newCategoryId: string) => {
    if (newCategoryId === currentCategoryId || isUpdating) {
      return;
    }

    // Apply optimistic update immediately
    onOptimisticUpdate?.(transactionId, newCategoryId);
    setIsUpdating(true);

    try {
      const response = await fetch('/api/transactions/update-category', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId,
          newCategoryId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update category');
      }

      // Success toast notification
      toast.success('Category updated successfully');
    } catch (error) {
      console.error('Error updating category:', error);

      // Revert optimistic update on error
      onOptimisticUpdate?.(transactionId, originalCategoryId);

      // Show error toast
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred while updating the category';
      toast.error(`Failed to update category: ${errorMessage}`);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className='w-48' onClick={(e) => e.stopPropagation()}>
      <CategorySelector
        selectedValue={currentCategoryId || ''}
        onValueChange={handleCategoryChange}
        categories={categories}
        disabled={isUpdating}
      />
    </div>
  );
}
