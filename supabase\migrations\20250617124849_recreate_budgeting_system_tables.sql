-- Step 1: Drop existing, outdated tables
DROP TABLE IF EXISTS public.budget_tracking CASCADE;
DROP TABLE IF EXISTS public.budgets CASCADE;

-- Step 2: Create new budgets table
CREATE TABLE "public"."budgets" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "user_id" uuid NOT NULL,
    "name" text NOT NULL,
    "start_date" date NOT NULL,
    "end_date" date NOT NULL,
    "total_income" numeric,
    "total_allocated" numeric NOT NULL DEFAULT 0,
    "is_active" boolean NOT NULL DEFAULT true,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    CONSTRAINT "budgets_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "budgets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users" ("id") ON DELETE CASCADE
);

-- Step 3: Create new budget_items table (Corrected FK to user_categories)
CREATE TABLE "public"."budget_items" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "budget_id" uuid NOT NULL,
    "category_id" uuid NOT NULL,
    "allocated_amount" numeric NOT NULL,
    "spent_amount" numeric NOT NULL DEFAULT 0,
    "notes" text,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    CONSTRAINT "budget_items_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "budget_items_budget_id_fkey" FOREIGN KEY ("budget_id") REFERENCES "public"."budgets" ("id") ON DELETE CASCADE,
    CONSTRAINT "budget_items_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."user_categories" ("id") ON DELETE CASCADE
);

-- Step 4: Create new budget_goals table
CREATE TABLE "public"."budget_goals" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "user_id" uuid NOT NULL,
    "name" text NOT NULL,
    "target_amount" numeric NOT NULL,
    "current_amount" numeric NOT NULL DEFAULT 0,
    "due_date" date,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    CONSTRAINT "budget_goals_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "budget_goals_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users" ("id") ON DELETE CASCADE
);

-- Step 5: Create the function to update spent_amount (Corrected to use user_category_id)
CREATE OR REPLACE FUNCTION public.update_budget_item_spending()
RETURNS TRIGGER AS $$
DECLARE
  v_user_id uuid;
  v_category_id uuid;
  v_transaction_date date;
  v_budget_id uuid;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    v_user_id := OLD.user_id;
    v_category_id := OLD.user_category_id;
    v_transaction_date := OLD.date;
  ELSE
    v_user_id := NEW.user_id;
    v_category_id := NEW.user_category_id;
    v_transaction_date := NEW.date;
  END IF;

  IF v_category_id IS NULL THEN
    RETURN NULL;
  END IF;

  SELECT id INTO v_budget_id
  FROM public.budgets
  WHERE user_id = v_user_id
    AND is_active = true
    AND v_transaction_date >= start_date
    AND v_transaction_date <= end_date
  LIMIT 1;

  IF v_budget_id IS NOT NULL THEN
    UPDATE public.budget_items
    SET spent_amount = (
      SELECT COALESCE(SUM(amount), 0)
      FROM public.transactions
      WHERE user_id = v_user_id
        AND user_category_id = v_category_id
        AND date >= (SELECT start_date FROM public.budgets WHERE id = v_budget_id)
        AND date <= (SELECT end_date FROM public.budgets WHERE id = v_budget_id)
    )
    WHERE budget_id = v_budget_id AND category_id = v_category_id;
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create the trigger on the transactions table
CREATE TRIGGER on_transaction_change_update_budget
AFTER INSERT OR UPDATE OR DELETE ON public.transactions
FOR EACH ROW EXECUTE FUNCTION public.update_budget_item_spending();

-- Step 7: RLS Policies
ALTER TABLE public.budgets ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage their own budgets" ON public.budgets
FOR ALL USING (auth.uid() = user_id);

ALTER TABLE public.budget_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can access items in their own budgets" ON public.budget_items
FOR ALL USING (auth.uid() = (SELECT user_id FROM public.budgets WHERE id = budget_id));

ALTER TABLE public.budget_goals ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage their own budget goals" ON public.budget_goals
FOR ALL USING (auth.uid() = user_id);;
