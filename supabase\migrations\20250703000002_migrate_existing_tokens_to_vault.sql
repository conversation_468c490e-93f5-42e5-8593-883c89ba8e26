-- Migrate existing Plaid access tokens from JSON metadata to Vault
-- This is a one-time migration to move tokens to secure storage

DO $$
DECLARE
    account_record RECORD;
    access_token TEXT;
    vault_secret_id UUID;
    secret_name TEXT;
BEGIN
    -- Process each financial account that has plaid_metadata with access_token
    FOR account_record IN 
        SELECT id, user_id, plaid_metadata 
        FROM financial_accounts 
        WHERE plaid_metadata IS NOT NULL 
        AND plaid_metadata::jsonb ? 'access_token'
        AND plaid_access_token_vault_id IS NULL  -- Only migrate accounts not already in Vault
    LOOP
        -- Extract access token from JSON metadata
        access_token := account_record.plaid_metadata::jsonb ->> 'access_token';
        
        IF access_token IS NOT NULL AND access_token != '' THEN
            -- Create unique secret name
            secret_name := 'plaid_access_token_' || account_record.id::TEXT;
            
            -- Store token in Vault
            BEGIN
                SELECT vault.create_secret(
                    access_token,
                    secret_name,
                    'Migrated Plaid access token for account ' || account_record.id::TEXT
                ) INTO vault_secret_id;
                
                -- Update financial_accounts table with vault reference
                UPDATE financial_accounts 
                SET plaid_access_token_vault_id = vault_secret_id
                WHERE id = account_record.id;
                
                RAISE NOTICE 'Migrated token for account % to Vault with ID %', 
                    account_record.id, vault_secret_id;
                    
            EXCEPTION WHEN OTHERS THEN
                RAISE WARNING 'Failed to migrate token for account %: %', 
                    account_record.id, SQLERRM;
            END;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Token migration completed. Check logs above for details.';
END $$;