'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface BudgetSummaryProps {
  income: number;
  allocated: number;
}

export function BudgetSummary({ income, allocated }: BudgetSummaryProps) {
  const remaining = income - allocated;
  const allocationPercentage = income > 0 ? (allocated / income) * 100 : 0;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getRemainingStatus = () => {
    if (remaining > 0) {
      return {
        icon: TrendingUp,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        label: 'Remaining',
      };
    } else if (remaining < 0) {
      return {
        icon: TrendingDown,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        label: 'Over Budget',
      };
    } else {
      return {
        icon: Minus,
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        label: 'Balanced',
      };
    }
  };

  const status = getRemainingStatus();
  const StatusIcon = status.icon;

  return (
    <Card className='border-2 border-dashed'>
      <CardHeader className='pb-3'>
        <CardTitle className='text-lg flex items-center gap-2'>
          <DollarSign className='h-5 w-5' />
          Budget Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          {/* Total Income */}
          <div className='text-center p-4 bg-blue-50 border border-blue-200 rounded-lg'>
            <p className='text-sm font-medium text-blue-700 mb-1'>Total Income</p>
            <p className='text-2xl font-bold text-blue-900'>
              {income > 0 ? formatCurrency(income) : 'Not set'}
            </p>
          </div>

          {/* Total Allocated */}
          <div className='text-center p-4 bg-purple-50 border border-purple-200 rounded-lg'>
            <p className='text-sm font-medium text-purple-700 mb-1'>Total Allocated</p>
            <p className='text-2xl font-bold text-purple-900'>{formatCurrency(allocated)}</p>
            {income > 0 && (
              <p className='text-xs text-purple-600 mt-1'>
                {allocationPercentage.toFixed(1)}% of income
              </p>
            )}
          </div>

          {/* Remaining/Status */}
          <div
            className={`text-center p-4 ${status.bgColor} border ${status.borderColor} rounded-lg`}
          >
            <div className='flex items-center justify-center gap-1 mb-1'>
              <StatusIcon className={`h-4 w-4 ${status.color}`} />
              <p className={`text-sm font-medium ${status.color}`}>{status.label}</p>
            </div>
            <p className={`text-2xl font-bold ${status.color}`}>
              {income > 0 ? formatCurrency(Math.abs(remaining)) : 'N/A'}
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        {income > 0 && (
          <div className='mt-4'>
            <div className='flex justify-between text-sm text-muted-foreground mb-2'>
              <span>Budget Allocation</span>
              <span>{allocationPercentage.toFixed(1)}%</span>
            </div>
            <div className='w-full bg-gray-200 rounded-full h-2'>
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  allocationPercentage > 100
                    ? 'bg-red-500'
                    : allocationPercentage > 90
                      ? 'bg-yellow-500'
                      : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(allocationPercentage, 100)}%` }}
              />
            </div>
            {allocationPercentage > 100 && (
              <p className='text-xs text-red-600 mt-1'>
                Warning: You have allocated more than your income!
              </p>
            )}
          </div>
        )}

        {/* Tips */}
        {income === 0 && (
          <div className='mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg'>
            <p className='text-sm text-yellow-800'>
              💡 <strong>Tip:</strong> Set your total income to see how much you have left to
              allocate.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
