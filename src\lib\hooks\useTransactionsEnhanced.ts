'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';

interface Transaction {
  id: string;
  amount: number;
  currency_code: string;
  transaction_date: string;
  authorized_date: string | null;
  posted_date: string | null;
  merchant_name: string | null;
  description: string;
  category_id: string | null;
  user_category_id: string | null;
  plaid_category: string[] | null;
  plaid_category_detailed: string[] | null;
  transaction_type: string;
  location: Record<string, unknown> | null;
  is_pending: boolean;
  is_recurring: boolean;
  status: string;
  tags: string[] | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  financial_accounts: {
    id: string;
    account_name: string;
    institution_name: string;
    account_type: string;
    account_subtype: string;
    mask: string | null;
  };
}

interface Category {
  id: string;
  name: string;
}

interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface TransactionsResponse {
  transactions: Transaction[];
  pagination: PaginationInfo;
}

interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

/**
 * Enhanced hook for desktop data table with:
 * - Initial load of 100 transactions
 * - Load More functionality (50 at a time)
 * - Client-side search on loaded data
 * - Server-side search all option
 * - Pre-fetched categories
 * - Server-side sorting
 */
export const useTransactionsEnhanced = (enabled: boolean = true) => {
  const [allTransactions, setAllTransactions] = useState<Transaction[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isSearchingAll, setIsSearchingAll] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);

  // Search state
  const [clientSearchQuery, setClientSearchQuery] = useState('');
  const [isSearchingAllData, setIsSearchingAllData] = useState(false);

  // Update transaction category optimistically
  const updateTransactionCategory = useCallback((transactionId: string, newCategoryId: string) => {
    setAllTransactions((prev) =>
      prev.map((transaction) =>
        transaction.id === transactionId
          ? { ...transaction, user_category_id: newCategoryId }
          : transaction
      )
    );
  }, []);

  // Fetch categories once
  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch('/api/categories/get');
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (err) {
      console.error('Error fetching categories:', err);
      // Don't fail the whole component if categories fail
    }
  }, []);

  // Initial fetch - 200 transactions + categories
  const fetchInitialData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch both transactions and categories in parallel
      const [transactionsResponse] = await Promise.allSettled([
        fetch(
          '/api/transactions/get?pageSize=100&page=1' +
            (sortConfig ? `&sortKey=${sortConfig.key}&sortOrder=${sortConfig.direction}` : '')
        ),
        fetchCategories(),
      ]);

      if (transactionsResponse.status === 'fulfilled' && transactionsResponse.value.ok) {
        const data: TransactionsResponse = await transactionsResponse.value.json();
        setAllTransactions(data.transactions);
        setTotalCount(data.pagination.totalCount);
        setHasMorePages(data.pagination.hasNextPage);
        setCurrentPage(1);
      } else {
        throw new Error('Failed to fetch transactions');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [sortConfig, fetchCategories]);

  // Load more transactions (100 at a time)
  const loadMoreTransactions = useCallback(async () => {
    if (!hasMorePages || isLoadingMore) return;

    setIsLoadingMore(true);
    try {
      const nextPage = currentPage + 1;
      const response = await fetch(
        `/api/transactions/get?pageSize=50&page=${nextPage}` +
          (sortConfig ? `&sortKey=${sortConfig.key}&sortOrder=${sortConfig.direction}` : '')
      );

      if (!response.ok) {
        throw new Error('Failed to load more transactions');
      }

      const data: TransactionsResponse = await response.json();
      setAllTransactions((prev) => [...prev, ...data.transactions]);
      setHasMorePages(data.pagination.hasNextPage);
      setCurrentPage(nextPage);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load more transactions');
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasMorePages, isLoadingMore, currentPage, sortConfig]);

  // Server-side search all transactions
  const searchAllTransactions = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setIsSearchingAllData(false);
      return;
    }

    setIsSearchingAll(true);
    try {
      const response = await fetch(
        `/api/transactions/get?searchQuery=${encodeURIComponent(searchQuery)}&pageSize=1000`
      );

      if (!response.ok) {
        throw new Error('Failed to search all transactions');
      }

      const data: TransactionsResponse = await response.json();
      setAllTransactions(data.transactions);
      setIsSearchingAllData(true);
      setHasMorePages(false); // Disable load more when searching all
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search transactions');
    } finally {
      setIsSearchingAll(false);
    }
  }, []);

  // Handle sorting
  const handleSort = useCallback((key: string, direction: 'asc' | 'desc') => {
    setSortConfig({ key, direction });
  }, []);

  // Reset to initial view
  const resetToInitialView = useCallback(() => {
    setIsSearchingAllData(false);
    setClientSearchQuery('');
    setSortConfig(null);
    fetchInitialData();
  }, [fetchInitialData]);

  // Client-side filtering for loaded transactions
  const filteredTransactions = useMemo(() => {
    if (!clientSearchQuery.trim()) {
      return allTransactions;
    }

    const query = clientSearchQuery.toLowerCase();
    return allTransactions.filter((transaction) => {
      const merchantName = (transaction.merchant_name || transaction.description).toLowerCase();
      const amount = transaction.amount.toString();
      const accountName = transaction.financial_accounts.account_name.toLowerCase();

      return merchantName.includes(query) || amount.includes(query) || accountName.includes(query);
    });
  }, [allTransactions, clientSearchQuery]);

  // Initial load (only if enabled)
  useEffect(() => {
    if (enabled) {
      fetchInitialData();
    }
  }, [fetchInitialData, enabled]);

  return {
    // Data
    transactions: filteredTransactions,
    categories,
    totalCount,

    // Loading states
    isLoading,
    isLoadingMore,
    isSearchingAll,

    // Error state
    error,

    // Pagination
    hasMorePages: hasMorePages && !isSearchingAllData,
    loadMoreTransactions,

    // Search
    clientSearchQuery,
    setClientSearchQuery,
    searchAllTransactions,
    isSearchingAllData,
    resetToInitialView,

    // Sorting
    sortConfig,
    handleSort,

    // Utilities
    retryFetch: fetchInitialData,
    updateTransactionCategory,
  };
};
