'use client';

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface SpendingAlertProps {
  categoryName: string;
  overage: number;
}

export const SpendingAlert: React.FC<SpendingAlertProps> = ({ categoryName, overage }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <Alert variant='destructive'>
      <AlertCircle className='h-4 w-4' />
      <AlertTitle>{categoryName} Overspent</AlertTitle>
      <AlertDescription>
        You are {formatCurrency(overage)} over budget in this category.
      </AlertDescription>
    </Alert>
  );
};
