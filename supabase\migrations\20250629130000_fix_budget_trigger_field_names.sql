-- Fix the budget trigger function that's causing transaction sync to fail
-- The trigger was trying to access NEW.date and OLD.date but the field is called transaction_date

CREATE OR REPLACE FUNCTION update_budget_item_spending()
RETURNS TRIGGER AS $$
DECLARE
    v_budget_item RECORD;
    v_transaction_date DATE;
    v_category_id UUID;
    v_amount DECIMAL;
BEGIN
    -- Handle DELETE operation
    IF TG_OP = 'DELETE' THEN
        v_transaction_date := OLD.transaction_date;  -- Fixed: was OLD.date
        v_category_id := OLD.category_id;
        v_amount := -OLD.amount;  -- Subtract the deleted amount
    ELSE
        -- Handle INSERT and UPDATE operations
        v_transaction_date := NEW.transaction_date;  -- Fixed: was NEW.date
        v_category_id := NEW.category_id;
        v_amount := NEW.amount;
        
        -- For UPDATE, also subtract the old amount
        IF TG_OP = 'UPDATE' THEN
            v_amount := v_amount - OLD.amount;
        END IF;
    END IF;

    -- Only process if we have a category
    IF v_category_id IS NOT NULL THEN
        -- Find the budget item for this category and transaction date
        SELECT bi.* INTO v_budget_item
        FROM budget_items bi
        JOIN budgets b ON bi.budget_id = b.id
        WHERE bi.category_id = v_category_id
          AND b.is_active = true
          AND v_transaction_date >= b.start_date 
          AND v_transaction_date <= b.end_date;

        -- Update the budget item if found
        IF FOUND THEN
            UPDATE budget_items 
            SET spent_amount = spent_amount + v_amount,
                updated_at = NOW()
            WHERE id = v_budget_item.id;
        END IF;
    END IF;

    -- Return the appropriate record
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;