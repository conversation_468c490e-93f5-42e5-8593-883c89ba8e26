CREATE TABLE transaction_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    changed_field TEXT NOT NULL,
    old_value TEXT,
    new_value TEXT,
    change_source TEXT NOT NULL, -- e.g., 'plaid_sync', 'user_edit'
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

ALTER TABLE transaction_audit_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own transaction audit logs"
ON transaction_audit_log FOR SELECT
USING (auth.uid() = user_id);

CREATE INDEX idx_audit_log_transaction_id ON transaction_audit_log(transaction_id);
CREATE INDEX idx_audit_log_user_id ON transaction_audit_log(user_id);;
