'use client';

import DashboardNav from '@/components/layout/DashboardNav';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <ProtectedRoute>
      <div className='flex h-screen bg-background'>
        {/* Sidebar Navigation */}
        <aside className='flex-shrink-0 border-r border-border'>
          <DashboardNav />
        </aside>

        {/* Main Content Area */}
        <main className='flex-1 overflow-auto'>
          <div className='h-full'>{children}</div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
