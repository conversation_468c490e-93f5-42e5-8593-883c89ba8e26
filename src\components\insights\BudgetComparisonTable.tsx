'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface BudgetComparisonData {
  categoryName: string;
  allocatedAmount: number;
  spentAmount: number;
  remainingAmount: number;
}

interface BudgetComparisonResponse {
  data: BudgetComparisonData[];
  metadata: {
    period: string;
    currency: string;
  };
}

const BudgetComparisonTable = () => {
  const [data, setData] = useState<BudgetComparisonData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/insights/budget-comparison?period=current_month');
        if (!response.ok) {
          throw new Error('Failed to fetch budget comparison data');
        }
        const result: BudgetComparisonResponse = await response.json();
        setData(result.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Budget vs. Actual</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className='flex justify-center items-center h-64'>
            <LoadingSpinner />
          </div>
        ) : error ? (
          <div className='flex justify-center items-center h-64 text-red-500'>Error: {error}</div>
        ) : data.length === 0 ? (
          <div className='flex justify-center items-center h-64'>
            <p>No budget data available for this month.</p>
          </div>
        ) : (
          <Table>
            <TableCaption>
              A comparison of your budget vs. actual spending for the current month.
            </TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead className='text-right'>Budgeted</TableHead>
                <TableHead className='text-right'>Spent</TableHead>
                <TableHead className='text-right'>Remaining</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((item, index) => (
                <TableRow key={item.categoryName || index}>
                  <TableCell className='font-medium'>{item.categoryName}</TableCell>
                  <TableCell className='text-right'>${item.allocatedAmount.toFixed(2)}</TableCell>
                  <TableCell className='text-right'>${item.spentAmount.toFixed(2)}</TableCell>
                  <TableCell
                    className={`text-right ${item.remainingAmount < 0 ? 'text-red-500 font-semibold' : 'text-green-600'}`}
                  >
                    ${item.remainingAmount.toFixed(2)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default BudgetComparisonTable;
