CREATE OR REPLACE FUNCTION update_transaction_category_manual(
    transaction_id_arg UUID,
    new_category_id_arg UUID
)
RETURNS VOID AS $$
DECLARE
    old_category_id UUID;
    current_user_id UUID := auth.uid();
BEGIN
    -- Get the old category ID for the audit log
    SELECT user_category_id
    INTO old_category_id
    FROM transactions
    WHERE id = transaction_id_arg AND user_id = current_user_id;

    -- Update the transaction's category
    UPDATE transactions
    SET user_category_id = new_category_id_arg,
        updated_at = NOW()
    WHERE id = transaction_id_arg AND user_id = current_user_id;

    -- Create an audit log entry for the change
    INSERT INTO transaction_audit_log (
        transaction_id,
        user_id,
        changed_field,
        old_value,
        new_value,
        change_source
    )
    VALUES (
        transaction_id_arg,
        current_user_id,
        'user_category_id',
        old_category_id::TEXT,
        new_category_id_arg::TEXT,
        'manual_override'
    );
END;
$$ LANGUAGE plpgsql;;
