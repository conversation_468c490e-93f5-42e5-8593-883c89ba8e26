import { SupabaseClient } from '@supabase/supabase-js';
import { Database, Tables, TablesInsert, TablesUpdate } from '@/lib/supabase/database.types';

type SupabaseClientType = SupabaseClient<Database>;

export class BudgetError extends Error {
  status: number;
  details?: string;

  constructor(message: string, status: number, details?: string) {
    super(message);
    this.name = 'BudgetError';
    this.status = status;
    this.details = details;
  }
}

export type Budget = Tables<'budgets'> & {
  items: (Tables<'budget_items'> & {
    category: { name: string } | null;
  })[];
};

export async function createBudget(
  supabase: SupabaseClientType,
  userId: string,
  budgetData: Omit<TablesInsert<'budgets'>, 'user_id'> & {
    items: Omit<TablesInsert<'budget_items'>, 'budget_id'>[];
  }
): Promise<Tables<'budgets'>> {
  const { items, ...mainBudgetData } = budgetData;

  const { data: budget, error: budgetError } = await supabase
    .from('budgets')
    .insert({ ...mainBudgetData, user_id: userId })
    .select()
    .single();

  if (budgetError) {
    console.error('Error creating budget:', budgetError);
    throw new BudgetError('Failed to create budget', 500, budgetError.message);
  }

  if (items && items.length > 0) {
    const budgetItems = items.map((item) => ({
      ...item,
      budget_id: budget.id,
    }));

    const { error: itemsError } = await supabase.from('budget_items').insert(budgetItems);

    if (itemsError) {
      console.error('Error creating budget items:', itemsError);
      // Rollback budget creation
      await supabase.from('budgets').delete().eq('id', budget.id);
      throw new BudgetError('Failed to create budget items', 500, itemsError.message);
    }
  }

  return budget;
}

export async function getBudgetByMonth(
  supabase: SupabaseClientType,
  userId: string,
  month: string // YYYY-MM
): Promise<Budget | null> {
  const [year, monthNum] = month.split('-').map(Number);
  const startDate = new Date(year, monthNum - 1, 1).toISOString().split('T')[0];
  const endDate = new Date(year, monthNum, 0).toISOString().split('T')[0];

  const { data: budget, error } = await supabase
    .from('budgets')
    .select(
      `
      *,
      items:budget_items (
        *,
        category:user_categories (name)
      )
    `
    )
    .eq('user_id', userId)
    .lte('start_date', endDate)
    .gt('end_date', startDate)
    .order('created_at', { ascending: false })
    .limit(1)
    .maybeSingle();

  if (error && error.code !== 'PGRST116') {
    // Ignore 'single row not found'
    console.error('Error fetching budget:', error);
    throw new BudgetError('Failed to retrieve budget', 500, error.message);
  }

  if (!budget) {
    return null;
  }

  // Calculate actual spending from transactions for each budget item
  const { data: transactions } = await supabase
    .from('transactions')
    .select('amount, user_category_id, transaction_date')
    .eq('user_id', userId)
    .gte('transaction_date', startDate)
    .lte('transaction_date', endDate);

  console.log(`Fetching transactions for ${month} (${startDate} to ${endDate}):`, transactions);

  // Calculate spending by category
  const spendingByCategory: Record<string, number> = {};
  if (transactions) {
    transactions.forEach((transaction) => {
      if (transaction.user_category_id) {
        spendingByCategory[transaction.user_category_id] =
          (spendingByCategory[transaction.user_category_id] || 0) + Math.abs(transaction.amount);
      }
    });
  }

  console.log('Spending by category:', spendingByCategory);

  // Update budget items with actual spending
  const updatedItems = budget.items.map((item) => ({
    ...item,
    spent_amount: spendingByCategory[item.category_id] || 0,
  }));

  // Calculate total allocated from budget items
  const totalAllocated = updatedItems.reduce((sum, item) => sum + item.allocated_amount, 0);

  const budgetWithSpending = {
    ...budget,
    total_allocated: totalAllocated,
    items: updatedItems,
  };

  return budgetWithSpending as Budget;
}

export async function updateBudget(
  supabase: SupabaseClientType,
  userId: string,
  budgetId: string,
  budgetData: TablesUpdate<'budgets'> & {
    items?: {
      id?: string;
      category_id: string;
      allocated_amount: number;
      notes?: string | null;
    }[];
  }
): Promise<Tables<'budgets'>> {
  const { items, ...mainBudgetData } = budgetData;

  // Verify budget ownership
  const { data: existingBudget, error: fetchError } = await supabase
    .from('budgets')
    .select('id')
    .eq('id', budgetId)
    .eq('user_id', userId)
    .single();

  if (fetchError || !existingBudget) {
    throw new BudgetError('Budget not found or access denied', 404);
  }

  const { data: updatedBudget, error: budgetError } = await supabase
    .from('budgets')
    .update(mainBudgetData)
    .eq('id', budgetId)
    .select()
    .single();

  if (budgetError) {
    console.error('Error updating budget:', budgetError);
    throw new BudgetError('Failed to update budget', 500, budgetError.message);
  }

  if (items) {
    // Separate existing items (with ID) from new items (without ID)
    const existingItems = items.filter((item) => item.id);
    const newItems = items.filter((item) => !item.id);

    // Update existing items
    if (existingItems.length > 0) {
      const updateItems = existingItems.map((item) => ({
        ...item,
        budget_id: budgetId,
      }));

      const { error: updateError } = await supabase.from('budget_items').upsert(updateItems);

      if (updateError) {
        console.error('Error updating budget items:', updateError);
        throw new BudgetError('Failed to update budget items', 500, updateError.message);
      }
    }

    // Insert new items
    if (newItems.length > 0) {
      const insertItems = newItems.map((item) => ({
        category_id: item.category_id,
        allocated_amount: item.allocated_amount,
        notes: item.notes,
        budget_id: budgetId,
      }));

      const { error: insertError } = await supabase.from('budget_items').insert(insertItems);

      if (insertError) {
        console.error('Error inserting budget items:', insertError);
        throw new BudgetError('Failed to insert budget items', 500, insertError.message);
      }
    }
  }

  return updatedBudget;
}

export async function deleteBudget(
  supabase: SupabaseClientType,
  userId: string,
  budgetId: string
): Promise<{ id: string }> {
  // Verify ownership
  const { data: existingBudget, error: fetchError } = await supabase
    .from('budgets')
    .select('id')
    .eq('id', budgetId)
    .eq('user_id', userId)
    .single();

  if (fetchError || !existingBudget) {
    throw new BudgetError('Budget not found or access denied', 404);
  }

  // First delete associated items
  const { error: itemsError } = await supabase
    .from('budget_items')
    .delete()
    .eq('budget_id', budgetId);

  if (itemsError) {
    console.error('Error deleting budget items:', itemsError);
    throw new BudgetError('Failed to delete budget items', 500, itemsError.message);
  }

  // Then delete the budget itself
  const { error: budgetError } = await supabase.from('budgets').delete().eq('id', budgetId);

  if (budgetError) {
    console.error('Error deleting budget:', budgetError);
    throw new BudgetError('Failed to delete budget', 500, budgetError.message);
  }

  return { id: budgetId };
}
export type BudgetSummary = {
  totalIncome: number;
  totalAllocated: number;
  totalSpent: number;
  netSavings: number;
  topSpendingCategories: {
    name: string;
    spent: number;
    allocated: number;
  }[];
};

export async function getBudgetSummary(
  supabase: SupabaseClientType,
  userId: string,
  month: string // YYYY-MM
): Promise<BudgetSummary | null> {
  const budget = await getBudgetByMonth(supabase, userId, month);

  if (!budget) {
    return null;
  }

  // Calculate actual spending from transactions for each budget item
  const [year, monthNum] = month.split('-').map(Number);
  const startDate = new Date(year, monthNum - 1, 1).toISOString().split('T')[0];
  const endDate = new Date(year, monthNum, 0).toISOString().split('T')[0];

  // Get transactions for this month grouped by category
  const { data: transactions } = await supabase
    .from('transactions')
    .select('amount, user_category_id')
    .eq('user_id', userId)
    .gte('transaction_date', startDate)
    .lte('transaction_date', endDate);

  // Calculate spending by category
  const spendingByCategory: Record<string, number> = {};
  if (transactions) {
    transactions.forEach((transaction) => {
      if (transaction.user_category_id) {
        spendingByCategory[transaction.user_category_id] =
          (spendingByCategory[transaction.user_category_id] || 0) + Math.abs(transaction.amount);
      }
    });
  }

  // Calculate totals with actual spending data
  const totalAllocated = budget.items.reduce((sum, item) => sum + (item.allocated_amount || 0), 0);
  const totalSpent = budget.items.reduce((sum, item) => {
    const actualSpent = spendingByCategory[item.category_id] || 0;
    return sum + actualSpent;
  }, 0);
  const totalIncome = budget.total_income || 0;
  const netSavings = totalIncome - totalSpent;

  const topSpendingCategories = budget.items
    .map((item) => ({
      name: item.category?.name || 'Uncategorized',
      spent: spendingByCategory[item.category_id] || 0,
      allocated: item.allocated_amount || 0,
    }))
    .filter((item) => item.spent > 0)
    .sort((a, b) => b.spent - a.spent)
    .slice(0, 5);

  return {
    totalIncome,
    totalAllocated,
    totalSpent,
    netSavings,
    topSpendingCategories,
  };
}

export type CategorySpendingRecommendation = {
  category_id: string;
  category_name: string;
  average_monthly_spending: number;
  recommended_budget: number;
};

export async function getAverageSpendingByCategory(
  supabase: SupabaseClientType,
  userId: string,
  months: number = 3
): Promise<CategorySpendingRecommendation[]> {
  // Calculate the date range for historical analysis
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - months);

  const startDateStr = startDate.toISOString().split('T')[0];
  const endDateStr = endDate.toISOString().split('T')[0];

  // Get transactions from the specified period
  const { data: transactions, error: transactionError } = await supabase
    .from('transactions')
    .select(
      `
      amount,
      user_category_id,
      transaction_date,
      user_categories (
        id,
        name
      )
    `
    )
    .eq('user_id', userId)
    .gte('transaction_date', startDateStr)
    .lte('transaction_date', endDateStr)
    .not('user_category_id', 'is', null);

  if (transactionError) {
    console.error('Error fetching transactions for recommendations:', transactionError);
    throw new BudgetError(
      'Failed to fetch transaction data for recommendations',
      500,
      transactionError.message
    );
  }

  if (!transactions || transactions.length === 0) {
    throw new BudgetError(
      'Insufficient transaction data for recommendations. Please ensure you have at least 2 months of categorized transactions.',
      400
    );
  }

  // Group transactions by category and calculate spending
  const categorySpending: Record<string, { total: number; name: string; transactions: number }> =
    {};

  transactions.forEach((transaction) => {
    if (transaction.user_category_id && transaction.user_categories) {
      const categoryId = transaction.user_category_id;
      const amount = Math.abs(transaction.amount); // Use absolute value for spending calculation

      if (!categorySpending[categoryId]) {
        categorySpending[categoryId] = {
          total: 0,
          name: transaction.user_categories.name,
          transactions: 0,
        };
      }

      categorySpending[categoryId].total += amount;
      categorySpending[categoryId].transactions += 1;
    }
  });

  // Calculate average monthly spending and recommendations
  const recommendations: CategorySpendingRecommendation[] = Object.entries(categorySpending)
    .map(([categoryId, data]) => {
      const averageMonthlySpending = data.total / months;
      // Add 10% buffer to recommended budget to account for variability
      const recommendedBudget = Math.round(averageMonthlySpending * 1.1);

      return {
        category_id: categoryId,
        category_name: data.name,
        average_monthly_spending: Math.round(averageMonthlySpending),
        recommended_budget: recommendedBudget,
      };
    })
    .filter((rec) => rec.average_monthly_spending > 0) // Only include categories with spending
    .sort((a, b) => b.average_monthly_spending - a.average_monthly_spending); // Sort by spending amount

  return recommendations;
}

export async function copyBudget(
  supabase: SupabaseClientType,
  userId: string,
  fromMonth: string, // YYYY-MM
  toMonth: string // YYYY-MM
): Promise<Tables<'budgets'>> {
  const sourceBudget = await getBudgetByMonth(supabase, userId, fromMonth);

  if (!sourceBudget) {
    throw new BudgetError(`No budget found for ${fromMonth} to copy from.`, 404);
  }

  const existingTargetBudget = await getBudgetByMonth(supabase, userId, toMonth);
  if (existingTargetBudget) {
    throw new BudgetError(`A budget already exists for ${toMonth}.`, 409);
  }

  const [toYear, toMonthNum] = toMonth.split('-').map(Number);
  const toStartDate = new Date(toYear, toMonthNum - 1, 1);
  const toEndDate = new Date(toYear, toMonthNum, 0);

  const newBudgetName = toStartDate.toLocaleString('en-US', { month: 'long', year: 'numeric' });

  const budgetToCreate: Omit<TablesInsert<'budgets'>, 'user_id'> & {
    items: Omit<TablesInsert<'budget_items'>, 'budget_id'>[];
  } = {
    name: newBudgetName,
    start_date: toStartDate.toISOString().split('T')[0],
    end_date: toEndDate.toISOString().split('T')[0],
    total_income: sourceBudget.total_income,
    is_active: true,
    items: sourceBudget.items.map((item) => ({
      category_id: item.category_id,
      allocated_amount: item.allocated_amount,
      notes: item.notes,
    })),
  };

  return createBudget(supabase, userId, budgetToCreate);
}
