# Implementation Plan: Task 1E - Budgeting System

**Author:** <PERSON><PERSON>, Architect AI
**Date:** 2025-06-16
**Status:** Proposed

## 1. Overview

This document provides a detailed technical plan for implementing the Budget Creation & Management features (Task 1E.1) as outlined in the [`dev-plan-01-foundation-mvp.md`](./dev-plan-01-foundation-mvp.md). The plan covers database schema modifications, API endpoint creation, frontend component development, and a phased implementation strategy.

The goal is to create a robust and user-friendly budgeting system that allows users to set up monthly budgets, track their spending against those budgets in real-time, and gain insights from analytics and reports.

## 2. Database Schema

To support the budgeting features, we will modify the existing `budgets` table and introduce two new tables: `budget_items` and `budget_goals`. We will also implement a server-side function to keep spending data synchronized.

### 2.1. <PERSON><PERSON><PERSON>ag<PERSON> (Mermaid)

```mermaid
erDiagram
    profiles {
        uuid id PK
        string email
    }

    transaction_categories {
        uuid id PK
        string name
    }

    budgets {
        uuid id PK
        uuid user_id FK
        string name
        date start_date
        date end_date
        numeric total_income "Nullable"
        numeric total_allocated
        boolean is_active
    }

    budget_items {
        uuid id PK
        uuid budget_id FK
        uuid category_id FK
        numeric allocated_amount
        numeric spent_amount
        string notes "Nullable"
    }

    budget_goals {
        uuid id PK
        uuid user_id FK
        string name
        numeric target_amount
        numeric current_amount
        date due_date
    }

    profiles ||--o{ budgets : "has"
    profiles ||--o{ budget_goals : "has"
    budgets ||--o{ budget_items : "contains"
    transaction_categories ||--o{ budget_items : "allocates to"
```

### 2.2. Table Definitions

#### `budgets` (Alterations to Existing Table)

This table will store the main budget record for a user for a specific period.

| Column Name       | Type          | Constraints                      | Description                                                  |
| :---------------- | :------------ | :------------------------------- | :----------------------------------------------------------- |
| `id`              | `uuid`        | **Primary Key**                  | Unique identifier for the budget.                            |
| `user_id`         | `uuid`        | **Foreign Key** -> `profiles.id` | The user who owns the budget.                                |
| `name`            | `text`        | `NOT NULL`                       | Name of the budget (e.g., "June 2025").                      |
| `start_date`      | `date`        | `NOT NULL`                       | The start date of the budget period.                         |
| `end_date`        | `date`        | `NOT NULL`                       | The end date of the budget period.                           |
| `total_income`    | `numeric`     | `NULL`                           | **(NEW)** User's planned total income for the period.        |
| `total_allocated` | `numeric`     | `NOT NULL`, `DEFAULT 0`          | **(RENAME)** Sum of all allocated amounts in `budget_items`. |
| `is_active`       | `boolean`     | `NOT NULL`, `DEFAULT true`       | Whether the budget is currently active.                      |
| `created_at`      | `timestamptz` | `DEFAULT now()`                  | Timestamp of creation.                                       |
| `updated_at`      | `timestamptz` | `DEFAULT now()`                  | Timestamp of last update.                                    |

#### `budget_items` (New Table)

This table replaces the old `budget_tracking` table and holds the allocated amount for each category within a budget.

| Column Name        | Type          | Constraints                                    | Description                                                    |
| :----------------- | :------------ | :--------------------------------------------- | :------------------------------------------------------------- |
| `id`               | `uuid`        | **Primary Key**                                | Unique identifier for the budget item.                         |
| `budget_id`        | `uuid`        | **Foreign Key** -> `budgets.id`                | The budget this item belongs to.                               |
| `category_id`      | `uuid`        | **Foreign Key** -> `transaction_categories.id` | The transaction category for this budget item.                 |
| `allocated_amount` | `numeric`     | `NOT NULL`                                     | The amount of money allocated to this category.                |
| `spent_amount`     | `numeric`     | `NOT NULL`, `DEFAULT 0`                        | The actual amount spent in this category. Updated via trigger. |
| `notes`            | `text`        | `NULL`                                         | User-provided notes for this budget item.                      |
| `created_at`       | `timestamptz` | `DEFAULT now()`                                | Timestamp of creation.                                         |
| `updated_at`       | `timestamptz` | `DEFAULT now()`                                | Timestamp of last update.                                      |

#### `budget_goals` (New Table)

This table will store user-defined financial goals.

| Column Name      | Type          | Constraints                      | Description                                  |
| :--------------- | :------------ | :------------------------------- | :------------------------------------------- |
| `id`             | `uuid`        | **Primary Key**                  | Unique identifier for the goal.              |
| `user_id`        | `uuid`        | **Foreign Key** -> `profiles.id` | The user who owns the goal.                  |
| `name`           | `text`        | `NOT NULL`                       | Name of the goal (e.g., "Vacation Fund").    |
| `target_amount`  | `numeric`     | `NOT NULL`                       | The financial target for the goal.           |
| `current_amount` | `numeric`     | `NOT NULL`, `DEFAULT 0`          | The amount currently saved towards the goal. |
| `due_date`       | `date`        | `NULL`                           | The target date to achieve the goal.         |
| `created_at`     | `timestamptz` | `DEFAULT now()`                  | Timestamp of creation.                       |
| `updated_at`     | `timestamptz` | `DEFAULT now()`                  | Timestamp of last update.                    |

### 2.3. Database Functions & Triggers

A Supabase database function will be created to automatically update the `spent_amount` in the `budget_items` table whenever a transaction is created, updated, or deleted.

**Function:** `update_budget_item_spending()`

- **Trigger:** Fires `AFTER INSERT OR UPDATE OR DELETE` on the `transactions` table.
- **Logic:**
  1.  Identifies the `budget_id` and `category_id` from the affected transaction.
  2.  Recalculates the total spending for that category within the budget's date range.
  3.  Updates the `spent_amount` in the corresponding `budget_items` record.

### 2.4. Row-Level Security (RLS) Policies

- **`budgets`:** Users can only `SELECT`, `INSERT`, `UPDATE`, `DELETE` their own budgets (`user_id = auth.uid()`).
- **`budget_items`:** Users can access items linked to budgets they own.
- **`budget_goals`:** Users can only manage their own goals (`user_id = auth.uid()`).

## 3. API Endpoints

The following API endpoints will be created under `/src/app/api/budgets/`.

### 3.1. Budget Management

- **`POST /api/budgets`**

  - **Description:** Creates a new budget and its associated category items.
  - **Request Body:** `{ name: string, startDate: string, endDate: string, totalIncome?: number, items: { categoryId: string, allocatedAmount: number }[] }`
  - **Response:** `{ id: string, ...budgetData }`

- **`GET /api/budgets?month=YYYY-MM`**

  - **Description:** Retrieves the budget for the specified month, including all items with their allocated and spent amounts.
  - **Response:** `{ id: string, name: string, ..., items: { id: string, category: { name: string }, allocatedAmount: number, spentAmount: number }[] }`

- **`PUT /api/budgets/{id}`**

  - **Description:** Updates an existing budget's details and its items.
  - **Request Body:** `{ name?: string, totalIncome?: number, items: { id?: string, categoryId: string, allocatedAmount: number }[] }`
  - **Response:** `{ id: string, ...updatedBudgetData }`

- **`DELETE /api/budgets/{id}`**

  - **Description:** Deletes a budget and all its associated items.
  - **Response:** `204 No Content`

- **`POST /api/budgets/copy`**
  - **Description:** Copies the budget items from a previous month to the current month.
  - **Request Body:** `{ fromMonth: "YYYY-MM", toMonth: "YYYY-MM" }`
  - **Response:** `{ id: string, ...newBudgetData }`

### 3.2. Analytics & Reporting

- **`GET /api/budgets/analytics/summary?month=YYYY-MM`**

  - **Description:** Retrieves a summary of budget performance for the month.
  - **Response:** `{ totalIncome: number, totalAllocated: number, totalSpent: number, netSavings: number, topSpendingCategories: { name: string, spent: number }[] }`

- **`GET /api/budgets/reports/monthly?month=YYYY-MM&format=csv`**
  - **Description:** Generates a downloadable monthly budget report.
  - **Response:** File stream (`text/csv` or `application/pdf`).

## 4. Frontend Components

The following React components will be created using TypeScript and shadcn/ui.

### 4.1. Component Diagram (Mermaid)

```mermaid
graph TD
    subgraph Pages
        A["/dashboard/budget"]
    end

    subgraph Budget Setup
        B[BudgetWizard] --> C{BudgetForm}
        C --> D[CategoryBudgetInput]
        C --> E[BudgetSummary]
    end

    subgraph Budget Tracking
        F[BudgetDashboard] --> G[BudgetProgress]
        F --> H[BudgetChart]
        F --> I[SpendingAlert]
    end

    subgraph Budget Analytics
        J[BudgetReportPage] --> K[AnalyticsCard]
        J --> L[TrendChart]
        J --> M[ExportButton]
    end

    A --> F
    A --> B
    A --> J
```

### 4.2. Component Definitions

#### Budget Setup Interface

- **`BudgetWizard`**: A multi-step modal for guiding users through their first budget setup.
  - **Props**: `{ onComplete: () => void; }`
- **`BudgetForm`**: A comprehensive form for creating or editing a budget.
  - **Props**: `{ budget?: BudgetWithItems; onSubmit: (data) => Promise<void>; }`
- **`CategoryBudgetInput`**: A single row within the `BudgetForm` for allocating funds to a category.
  - **Props**: `{ category: Category; value: number; onChange: (value: number) => void; }`
- **`BudgetSummary`**: A small component displaying `Income vs. Allocated`.
  - **Props**: `{ income: number; allocated: number; }`

#### Budget Tracking & Visualization

- **`BudgetDashboard`**: The main container on the budget page, displaying the current state of the budget.
  - **Props**: `{ month: Date; }`
- **`BudgetProgress`**: A reusable progress bar component.
  - **Props**: `{ title: string; allocated: number; spent: number; }`
- **`BudgetChart`**: A chart for visualizing budget data (e.g., Recharts Donut Chart).
  - **Props**: `{ data: { name: string; spent: number; allocated: number }[]; }`
- **`SpendingAlert`**: A dismissible alert for overspent categories.
  - **Props**: `{ categoryName: string; overage: number; }`

#### Budget Analytics & Reporting

- **`BudgetReport`**: A component to display a formatted monthly report.
  - **Props**: `{ reportData: MonthlyReportData; }`
- **`AnalyticsCard`**: A card to display a single, key metric.
  - **Props**: `{ title: string; value: string | number; change?: number; }`
- **`TrendChart`**: A line or bar chart to show spending trends.
  - **Props**: `{ data: { date: string; spent: number; }[]; }`
- **`ExportButton`**: A button that triggers a report download via the API.
  - **Props**: `{ month: Date; format: 'csv' | 'pdf'; }`

## 5. Implementation Steps

The implementation will be broken down into the following phases:

1.  **Phase 1: Database & Backend Foundation**

    1.  Create a new Supabase migration file for the schema changes (`budgets`, `budget_items`, `budget_goals`).
    2.  Implement the `update_budget_item_spending()` database function and its trigger.
    3.  Apply the RLS policies to all new and modified tables.
    4.  Build the core API endpoints for budget CRUD: `POST`, `GET`, `PUT`, `DELETE /api/budgets`.

2.  **Phase 2: Budget Setup UI**

    1.  Create the new page at `/dashboard/budget`.
    2.  Develop the `BudgetForm` component for creating and editing a budget.
    3.  Integrate the form with the API endpoints created in Phase 1.
    4.  Develop the `BudgetWizard` component for a guided setup experience for new users.

3.  **Phase 3: Budget Tracking & Visualization**

    1.  Develop the `BudgetDashboard` to display the current month's budget.
    2.  Implement the `BudgetProgress` and `BudgetChart` components.
    3.  Fetch real-time data from the `GET /api/budgets?month=...` endpoint to populate the tracking components.
    4.  Implement the `SpendingAlert` component based on the fetched data.

4.  **Phase 4: Analytics, Reporting & Final Touches**
    1.  Implement the backend endpoints for analytics and reporting (`/api/budgets/analytics/summary`, `/api/budgets/reports/monthly`).
    2.  Develop the frontend components for analytics: `AnalyticsCard`, `TrendChart`, and `ExportButton`.
    3.  Implement the "Copy from Previous Month" functionality.
    4.  Conduct thorough end-to-end testing of the entire budgeting flow.

## 6. Next Steps

This plan is now ready for review. Upon approval, it can be used to create specific tasks for developers. The next step is to begin Phase 1 by creating the Supabase migration.
