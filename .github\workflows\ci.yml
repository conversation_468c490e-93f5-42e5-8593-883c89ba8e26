name: NAVsync CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  build-and-test:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository)
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [22.x]

    env:
      NODE_ENV: test
      # Test environment values - Plaid variables are optional in test mode per env.mjs
      PLAID_CLIENT_ID: test-client-id
      PLAID_SECRET_SANDBOX: test-secret-sandbox
      PLAID_SECRET_DEVELOPMENT: test-secret-development
      PLAID_ENV: sandbox
      NEXT_PUBLIC_SUPABASE_URL: https://test.supabase.co
      NEXT_PUBLIC_SUPABASE_ANON_KEY: test-anon-key

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linters
        run: npm run lint

      - name: Run type checking
        run: npm run type-check

      - name: Run security audit
        run: npm run audit

  actionlint:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository)
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - uses: actions/checkout@v4
      - name: Check workflow files
        uses: reviewdog/action-actionlint@v1
        with:
          reporter: github-pr-review

  gitleaks:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository)
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: Detect secrets
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
