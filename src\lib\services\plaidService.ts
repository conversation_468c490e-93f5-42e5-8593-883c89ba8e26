import { createSupabaseServerClient } from '@/lib/supabase/server';

interface FinancialAccountData {
  userId: string;
  accessToken: string;
  itemId: string;
  institutionName: string;
  institutionId: string;
}

export async function createFinancialAccount(data: FinancialAccountData) {
  const supabase = await createSupabaseServerClient();
  const { userId, accessToken, itemId, institutionName, institutionId } = data;

  // First, insert the financial account record
  const { data: accountData, error: insertError } = await supabase
    .from('financial_accounts')
    .insert({
      user_id: userId,
      plaid_item_id: itemId,
      plaid_account_id: `temp_${itemId}`,
      account_name: `${institutionName} Account`,
      account_type: 'other',
      institution_name: institutionName,
      institution_id: institutionId,
      plaid_metadata: {
        item_id: itemId,
        institution: {
          name: institutionName,
          institution_id: institutionId,
        },
        exchange_timestamp: new Date().toISOString(),
      },
    })
    .select('id')
    .single();

  if (insertError || !accountData) {
    console.error('Database insertion error:', insertError);
    throw new Error('Failed to store account credentials');
  }

  // Store access token securely in Vault
  const { error: vaultError } = await supabase.rpc('store_plaid_access_token', {
    p_user_id: userId,
    p_account_id: accountData.id,
    p_access_token: accessToken,
    p_description: `Plaid access token for ${institutionName}`,
  });

  if (vaultError) {
    console.error('Vault storage error:', vaultError);
    // Clean up the financial account record if vault storage fails
    await supabase.from('financial_accounts').delete().eq('id', accountData.id);
    throw new Error('Failed to securely store access token');
  }

  return { ok: true };
}
