import { createSupabaseServerClient } from '@/lib/supabase/server';
import { BudgetError, createBudget, getBudgetByMonth } from '@/lib/services/budgetService';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const budgetItemSchema = z.object({
  categoryId: z.string().uuid(),
  allocatedAmount: z.number().positive(),
});

const createBudgetSchema = z.object({
  name: z.string().min(1),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  totalIncome: z.number().optional(),
  items: z.array(budgetItemSchema),
});

export async function POST(request: Request) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const body = await request.json();
  const validation = createBudgetSchema.safeParse(body);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid request body', details: validation.error.flatten() },
      { status: 400 }
    );
  }

  const { items, ...budgetData } = validation.data;

  const budgetInsert = {
    name: budgetData.name,
    start_date: budgetData.startDate,
    end_date: budgetData.endDate,
    total_income: budgetData.totalIncome,
  };

  const itemsInsert = items.map((item) => ({
    category_id: item.categoryId,
    allocated_amount: item.allocatedAmount,
  }));

  try {
    // The service will handle associating the items with the new budget
    const newBudget = await createBudget(supabase, user.id, {
      ...budgetInsert,
      items: itemsInsert,
    });
    return NextResponse.json(newBudget, { status: 201 });
  } catch (error) {
    if (error instanceof BudgetError) {
      return NextResponse.json(
        { error: error.message, details: error.details },
        { status: error.status }
      );
    }
    console.error('Unhandled error in POST /api/budgets:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

export async function GET(request: Request) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const month = searchParams.get('month'); // YYYY-MM

  if (!month || !/^\d{4}-\d{2}$/.test(month)) {
    return NextResponse.json(
      { error: 'Invalid or missing month parameter. Use YYYY-MM format.' },
      { status: 400 }
    );
  }

  try {
    const budget = await getBudgetByMonth(supabase, user.id, month);
    if (!budget) {
      return NextResponse.json(
        { message: 'No budget found for the specified month' },
        { status: 404 }
      );
    }
    return NextResponse.json(budget);
  } catch (error) {
    if (error instanceof BudgetError) {
      return NextResponse.json(
        { error: error.message, details: error.details },
        { status: error.status }
      );
    }
    console.error('Unhandled error in GET /api/budgets:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
