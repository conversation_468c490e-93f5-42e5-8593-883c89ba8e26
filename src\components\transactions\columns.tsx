'use client';

import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Transaction } from './TransactionCard';
import InlineCategorySelector from './InlineCategorySelector';

interface Category {
  id: string;
  name: string;
}

interface ColumnsProps {
  onTransactionClick: (transaction: Transaction) => void;
  categories: Category[];
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  onOptimisticUpdate?: (transactionId: string, categoryId: string) => void;
}

export const createColumns = ({
  onTransactionClick,
  categories,
  onSort,
  onOptimisticUpdate,
}: ColumnsProps): ColumnDef<Transaction>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        ref={(input) => {
          if (input) input.indeterminate = table.getIsSomePageRowsSelected();
        }}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
        onClick={(e) => e.stopPropagation()}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'transaction_date',
    header: ({ column }) => (
      <Button
        variant='ghost'
        onClick={() => {
          const isAsc = column.getIsSorted() === 'asc';
          const newDirection = isAsc ? 'desc' : 'asc';
          column.toggleSorting(isAsc);
          onSort?.('transaction_date', newDirection);
        }}
        className='h-8 px-2 lg:px-3 hover:bg-gray-100/50 font-semibold justify-start'
      >
        Date
        <ArrowUpDown className='ml-2 h-3 w-3' />
      </Button>
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('transaction_date'));
      return (
        <div className='text-sm'>
          {date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })}
        </div>
      );
    },
  },
  {
    accessorKey: 'merchant_name',
    header: ({ column }) => (
      <Button
        variant='ghost'
        onClick={() => {
          const isAsc = column.getIsSorted() === 'asc';
          const newDirection = isAsc ? 'desc' : 'asc';
          column.toggleSorting(isAsc);
          onSort?.('merchant_name', newDirection);
        }}
        className='h-8 px-2 lg:px-3 hover:bg-gray-100/50 font-semibold justify-start'
      >
        Merchant
        <ArrowUpDown className='ml-2 h-3 w-3' />
      </Button>
    ),
    cell: ({ row }) => {
      const transaction = row.original;
      const displayName = transaction.merchant_name || transaction.description;
      const accountInfo = transaction.financial_accounts;

      return (
        <div className='min-w-0'>
          <div className='font-medium text-sm text-gray-900 truncate'>{displayName}</div>
          <div className='text-xs text-gray-500 truncate'>
            {accountInfo.account_name} • {accountInfo.institution_name}
            {accountInfo.mask && ` • ****${accountInfo.mask}`}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'user_category_id',
    header: 'Category',
    cell: ({ row }) => {
      const transaction = row.original;

      return (
        <InlineCategorySelector
          transactionId={transaction.id}
          currentCategoryId={transaction.user_category_id || ''}
          categories={categories}
          onOptimisticUpdate={onOptimisticUpdate}
        />
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: 'amount',
    header: ({ column }) => (
      <Button
        variant='ghost'
        onClick={() => {
          const isAsc = column.getIsSorted() === 'asc';
          const newDirection = isAsc ? 'desc' : 'asc';
          column.toggleSorting(isAsc);
          onSort?.('amount', newDirection);
        }}
        className='h-8 px-2 lg:px-3 hover:bg-gray-100/50 font-semibold justify-end'
      >
        Amount
        <ArrowUpDown className='ml-2 h-3 w-3' />
      </Button>
    ),
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('amount'));
      const currencyCode = row.original.currency_code;
      const isNegative = amount < 0;
      const absoluteAmount = Math.abs(amount);

      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode || 'USD',
      }).format(absoluteAmount);

      return (
        <div className='text-right'>
          <div
            className={`font-semibold text-sm ${isNegative ? 'text-red-600' : 'text-green-600'}`}
          >
            {isNegative ? '-' : '+'}
            {formatted}
          </div>
          <div className='text-xs text-gray-500'>{row.original.transaction_type}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'financial_accounts.account_name',
    header: 'Account',
    cell: ({ row }) => {
      const account = row.original.financial_accounts;
      return (
        <div className='text-sm'>
          <div className='font-medium text-gray-900'>{account.account_name}</div>
          <div className='text-xs text-gray-500'>{account.account_type}</div>
        </div>
      );
    },
  },
  {
    id: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const transaction = row.original;
      return (
        <div className='flex flex-col gap-1'>
          {transaction.is_pending && (
            <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800'>
              Pending
            </span>
          )}
          {transaction.is_recurring && (
            <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
              Recurring
            </span>
          )}
          {!transaction.is_pending && !transaction.is_recurring && (
            <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800'>
              Posted
            </span>
          )}
        </div>
      );
    },
    enableSorting: false,
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const transaction = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant='ghost'
              className='h-8 w-8 p-0 hover:bg-gray-100'
              onClick={(e) => e.stopPropagation()}
            >
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={() => onTransactionClick(transaction)}>
              View details
            </DropdownMenuItem>
            <DropdownMenuItem disabled>Split transaction</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
