'use client';

import React from 'react';
import {} from '@/components/ui/card';

interface BudgetProgressProps {
  title: string;
  allocated: number;
  spent: number;
  budgetMonth?: string; // YYYY-MM format for month-specific pacing
  showPacing?: boolean; // Whether to show pacing indicator
}

export const BudgetProgress: React.FC<BudgetProgressProps> = ({
  title,
  allocated,
  spent,
  budgetMonth,
  showPacing = true,
}) => {
  const progress = allocated > 0 ? Math.min((spent / allocated) * 100, 100) : 0;
  const overspent = spent > allocated;

  // Calculate pacing percentage
  const calculatePacingPercentage = () => {
    if (!budgetMonth || !showPacing) return 0;

    const now = new Date();
    const [year, month] = budgetMonth.split('-').map(Number);
    const budgetYear = year;
    const budgetMonthNum = month;

    // Only show pacing for current month
    if (now.getFullYear() !== budgetYear || now.getMonth() + 1 !== budgetMonthNum) {
      return 0;
    }

    const currentDay = now.getDate();
    const totalDaysInMonth = new Date(budgetYear, budgetMonthNum, 0).getDate();

    return (currentDay / totalDaysInMonth) * 100;
  };

  const pacingPercentage = calculatePacingPercentage();
  const shouldShowPacing = pacingPercentage > 0 && showPacing;

  // Determine pacing status
  const getPacingStatus = () => {
    if (!shouldShowPacing) return null;

    const expectedSpending = (allocated * pacingPercentage) / 100;
    const difference = spent - expectedSpending;
    const percentageDiff = expectedSpending > 0 ? (difference / expectedSpending) * 100 : 0;

    if (Math.abs(percentageDiff) < 10) return 'on-track';
    if (percentageDiff > 0) return 'ahead';
    return 'behind';
  };

  const pacingStatus = getPacingStatus();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getPacingStatusText = () => {
    if (!shouldShowPacing || !pacingStatus) return '';

    const expectedSpending = (allocated * pacingPercentage) / 100;

    switch (pacingStatus) {
      case 'on-track':
        return 'On track';
      case 'ahead':
        return `$${Math.abs(spent - expectedSpending).toFixed(0)} ahead of pace`;
      case 'behind':
        return `$${Math.abs(spent - expectedSpending).toFixed(0)} behind pace`;
      default:
        return '';
    }
  };

  return (
    <div className='flex items-center gap-4'>
      <div className='flex-1'>
        <div className='flex justify-between items-center mb-1'>
          <p className='font-medium text-sm'>{title}</p>
          <div className='text-right'>
            <p
              className={`text-sm font-semibold ${overspent ? 'text-destructive' : 'text-muted-foreground'}`}
            >
              {formatCurrency(spent)} / {formatCurrency(allocated)}
            </p>
            {shouldShowPacing && pacingStatus && (
              <p
                className={`text-xs ${
                  pacingStatus === 'on-track'
                    ? 'text-green-600'
                    : pacingStatus === 'ahead'
                      ? 'text-orange-600'
                      : 'text-blue-600'
                }`}
              >
                {getPacingStatusText()}
              </p>
            )}
          </div>
        </div>
        <div className='w-full bg-muted rounded-full h-2.5 relative'>
          {/* Main progress bar */}
          <div
            className={`h-2.5 rounded-full ${overspent ? 'bg-destructive' : 'bg-primary'}`}
            style={{ width: `${progress}%` }}
          ></div>

          {/* Pacing indicator line */}
          {shouldShowPacing && pacingPercentage > 0 && (
            <div
              className='absolute top-0 h-2.5 w-0.5 bg-gray-800 opacity-70'
              style={{
                left: `${Math.min(pacingPercentage, 100)}%`,
                transform: 'translateX(-50%)',
              }}
              title={`Expected spending by today: ${formatCurrency((allocated * pacingPercentage) / 100)}`}
            >
              {/* Small indicator dot */}
              <div className='absolute -top-1 -left-1 w-2 h-2 bg-gray-800 rounded-full opacity-70'></div>
            </div>
          )}
        </div>

        {/* Pacing legend (only for current month) */}
        {shouldShowPacing && (
          <div className='flex justify-between items-center mt-1 text-xs text-muted-foreground'>
            <span>Progress</span>
            <span>Expected pace ▲</span>
          </div>
        )}
      </div>
    </div>
  );
};
