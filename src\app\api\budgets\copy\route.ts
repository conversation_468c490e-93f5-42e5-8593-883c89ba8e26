import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { BudgetError, copyBudget } from '@/lib/services/budgetService';

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { fromMonth, toMonth } = await request.json();

  if (!fromMonth || !/^\d{4}-\d{2}$/.test(fromMonth)) {
    return NextResponse.json({ error: 'fromMonth is required in YYYY-MM format' }, { status: 400 });
  }

  if (!toMonth || !/^\d{4}-\d{2}$/.test(toMonth)) {
    return NextResponse.json({ error: 'toMonth is required in YYYY-MM format' }, { status: 400 });
  }

  try {
    const newBudget = await copyBudget(supabase, user.id, fromMonth, toMonth);
    return NextResponse.json(newBudget);
  } catch (error) {
    if (error instanceof BudgetError) {
      return NextResponse.json(
        { error: error.message, details: error.details },
        { status: error.status }
      );
    }
    console.error('Error copying budget:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
