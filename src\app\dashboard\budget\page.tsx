'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Plus, Calendar, DollarSign, TrendingUp, PiggyBank, Target, Copy } from 'lucide-react';
import {
  BudgetForm,
  BudgetWizard,
  BudgetDashboard,
  AnalyticsCard,
  TrendChart,
} from '@/components/budget';
import { Budget } from '@/lib/services/budgetService';
import { useBudgetAnalytics } from '@/lib/hooks/useBudgetAnalytics';
import { toast } from 'sonner';

export default function BudgetPage() {
  const [currentBudget, setCurrentBudget] = useState<Budget | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showBudgetForm, setShowBudgetForm] = useState(false);
  const [showWizard, setShowW<PERSON>rd] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCopyingBudget, setIsCopyingBudget] = useState(false);

  // Get current month in YYYY-MM format
  const getCurrentMonth = () => {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  };

  const [selectedMonth, setSelectedMonth] = useState(getCurrentMonth());

  // Analytics hook
  const {
    analytics,
    isLoading: analyticsLoading,
    error: analyticsError,
  } = useBudgetAnalytics(selectedMonth);

  // Fetch budget for the selected month
  const fetchBudget = async (month: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/budgets?month=${month}`);

      if (response.status === 404) {
        setCurrentBudget(null);
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to fetch budget');
      }

      const budget = await response.json();
      setCurrentBudget(budget);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch budget');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBudget(selectedMonth);
  }, [selectedMonth]);

  const handleBudgetCreated = () => {
    setShowBudgetForm(false);
    setShowWizard(false);
    fetchBudget(selectedMonth);
  };

  const handleBudgetUpdated = () => {
    setShowBudgetForm(false);
    fetchBudget(selectedMonth);
  };

  const handleCopyBudget = async () => {
    try {
      setIsCopyingBudget(true);

      // Calculate previous month
      const [year, month] = selectedMonth.split('-').map(Number);
      const prevDate = new Date(year, month - 2, 1); // month - 2 because month is 1-indexed
      const prevMonth = `${prevDate.getFullYear()}-${String(prevDate.getMonth() + 1).padStart(2, '0')}`;

      const response = await fetch('/api/budgets/copy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromMonth: prevMonth,
          toMonth: selectedMonth,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to copy budget');
      }

      toast.success('Budget copied successfully!');
      fetchBudget(selectedMonth);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to copy budget';
      toast.error(errorMessage);
    } finally {
      setIsCopyingBudget(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatMonthYear = (monthStr: string) => {
    const [year, month] = monthStr.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center h-full'>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className='p-6 space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>Budget</h1>
          <p className='text-muted-foreground'>
            Manage your monthly budget for {formatMonthYear(selectedMonth)}
          </p>
        </div>

        <div className='flex items-center gap-4'>
          {/* Month Selector */}
          <div className='flex items-center gap-2'>
            <Calendar className='h-4 w-4' />
            <input
              type='month'
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
              className='px-3 py-2 border border-input rounded-md bg-background text-foreground'
            />
          </div>

          {currentBudget ? (
            <div className='flex gap-2'>
              <Button onClick={() => setShowBudgetForm(true)}>Edit Budget</Button>
              <Button onClick={handleCopyBudget} variant='outline' disabled={isCopyingBudget}>
                <Copy className='h-4 w-4 mr-2' />
                {isCopyingBudget ? 'Copying...' : 'Copy from Previous'}
              </Button>
            </div>
          ) : (
            <div className='flex gap-2'>
              <Button onClick={() => setShowWizard(true)} variant='default'>
                <Plus className='h-4 w-4 mr-2' />
                Create Budget
              </Button>
              <Button onClick={() => setShowBudgetForm(true)} variant='outline'>
                Quick Create
              </Button>
              <Button onClick={handleCopyBudget} variant='outline' disabled={isCopyingBudget}>
                <Copy className='h-4 w-4 mr-2' />
                {isCopyingBudget ? 'Copying...' : 'Copy from Previous'}
              </Button>
            </div>
          )}
        </div>
      </div>

      {error && (
        <Card className='border-destructive'>
          <CardContent className='pt-6'>
            <p className='text-destructive'>{error}</p>
          </CardContent>
        </Card>
      )}

      {analyticsError && (
        <Card className='border-destructive'>
          <CardContent className='pt-6'>
            <p className='text-destructive'>Analytics Error: {analyticsError}</p>
          </CardContent>
        </Card>
      )}

      {/* Budget Content */}
      {currentBudget ? (
        <div className='grid gap-6'>
          {/* Budget Overview */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Total Income</CardTitle>
                <DollarSign className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {currentBudget.total_income
                    ? formatCurrency(currentBudget.total_income)
                    : 'Not set'}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Total Allocated</CardTitle>
                <DollarSign className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {formatCurrency(currentBudget.total_allocated)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Remaining</CardTitle>
                <DollarSign className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {currentBudget.total_income
                    ? formatCurrency(currentBudget.total_income - currentBudget.total_allocated)
                    : 'N/A'}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Analytics Section */}
          {analyticsLoading ? (
            <Card>
              <CardContent className='flex items-center justify-center py-8'>
                <LoadingSpinner />
                <span className='ml-2'>Loading analytics...</span>
              </CardContent>
            </Card>
          ) : analytics ? (
            <div className='space-y-6'>
              <div className='flex items-center justify-between'>
                <h2 className='text-xl font-semibold'>Analytics</h2>
              </div>

              {/* Analytics Cards */}
              <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
                <AnalyticsCard
                  title='Total Spent'
                  value={formatCurrency(analytics.totalSpent)}
                  icon={DollarSign}
                  description='Amount spent this month'
                />
                <AnalyticsCard
                  title='Net Savings'
                  value={formatCurrency(analytics.netSavings)}
                  icon={PiggyBank}
                  description='Income minus spending'
                />
                <AnalyticsCard
                  title='Budget Utilization'
                  value={`${Math.round((analytics.totalSpent / analytics.totalAllocated) * 100)}%`}
                  icon={Target}
                  description='Percentage of budget used'
                />
                <AnalyticsCard
                  title='Categories Tracked'
                  value={analytics.topSpendingCategories.length.toString()}
                  icon={TrendingUp}
                  description='Active spending categories'
                />
              </div>

              {/* Trend Chart */}
              {analytics.topSpendingCategories.length > 0 && (
                <TrendChart
                  title='Top Spending Categories'
                  data={{
                    labels: analytics.topSpendingCategories.map((cat) => cat.name),
                    datasets: [
                      {
                        label: 'Allocated',
                        data: analytics.topSpendingCategories.map((cat) => cat.allocated),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                      },
                      {
                        label: 'Spent',
                        data: analytics.topSpendingCategories.map((cat) => cat.spent),
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        fill: true,
                      },
                    ],
                  }}
                  height={250}
                />
              )}
            </div>
          ) : null}

          <BudgetDashboard budget={currentBudget} />
        </div>
      ) : (
        /* No Budget State */
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-12'>
            <div className='text-center space-y-4'>
              <div className='w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center'>
                <DollarSign className='h-8 w-8 text-muted-foreground' />
              </div>
              <div>
                <h3 className='text-lg font-semibold'>No Budget Found</h3>
                <p className='text-muted-foreground'>
                  You haven&apos;t created a budget for {formatMonthYear(selectedMonth)} yet.
                </p>
              </div>
              <div className='flex gap-2 justify-center'>
                <Button onClick={() => setShowWizard(true)}>
                  <Plus className='h-4 w-4 mr-2' />
                  Create Your First Budget
                </Button>
                <Button onClick={() => setShowBudgetForm(true)} variant='outline'>
                  Quick Create
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Budget Form Modal */}
      {showBudgetForm && (
        <BudgetForm
          budget={currentBudget}
          month={selectedMonth}
          onSubmit={currentBudget ? handleBudgetUpdated : handleBudgetCreated}
          onCancel={() => setShowBudgetForm(false)}
        />
      )}

      {/* Budget Wizard Modal */}
      {showWizard && (
        <BudgetWizard
          month={selectedMonth}
          onComplete={handleBudgetCreated}
          onCancel={() => setShowWizard(false)}
        />
      )}
    </div>
  );
}
