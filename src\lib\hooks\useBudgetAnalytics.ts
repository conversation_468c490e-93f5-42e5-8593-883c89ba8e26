'use client';

import { useState, useEffect } from 'react';
import { BudgetSummary } from '@/lib/services/budgetService';

export function useBudgetAnalytics(month: string) {
  const [analytics, setAnalytics] = useState<BudgetSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = async (targetMonth: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/budgets/analytics/summary?month=${targetMonth}`);

      if (response.status === 404) {
        setAnalytics(null);
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }

      const data = await response.json();
      setAnalytics(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics');
      setAnalytics(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (month) {
      fetchAnalytics(month);
    }
  }, [month]);

  return {
    analytics,
    isLoading,
    error,
    refetch: () => fetchAnalytics(month),
  };
}
