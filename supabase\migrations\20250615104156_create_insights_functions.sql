CREATE OR R<PERSON>LACE FUNCTION get_category_spending(p_user_id UUID, p_start_date DATE, p_end_date DATE)
RETURNS TABLE(category_id UUID, category_name TEXT, total_spent NUMERIC) AS $$
BEGIN
    RETURN QUERY
    SELECT
        uc.id AS category_id,
        uc.name AS category_name,
        COALESCE(SUM(t.amount), 0) AS total_spent
    FROM
        user_categories uc
    LEFT JOIN
        transactions t ON uc.id = t.user_category_id
        AND t.user_id = p_user_id
        AND t.transaction_date >= p_start_date
        AND t.transaction_date <= p_end_date
        AND t.amount > 0
    WHERE
        uc.user_id = p_user_id
    GROUP BY
        uc.id, uc.name
    ORDER BY
        total_spent DESC;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_budget_vs_actual(p_user_id UUID, p_budget_period TEXT)
RETURNS TABLE(
    category_id UUID,
    category_name TEXT,
    allocated_amount NUMERIC,
    spent_amount NUMERIC,
    remaining_amount NUMERIC
) AS $$
DECLARE
    v_budget_id UUID;
    v_start_date DATE;
    v_end_date DATE;
BEGIN
    SELECT id, start_date, end_date INTO v_budget_id, v_start_date, v_end_date
    FROM budgets
    WHERE user_id = p_user_id AND budget_period = p_budget_period AND is_active = TRUE
    LIMIT 1;

    IF v_budget_id IS NULL THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT
        bt.category_id,
        uc.name AS category_name,
        bt.allocated_amount,
        COALESCE(s.total_spent, 0) AS spent_amount,
        (bt.allocated_amount - COALESCE(s.total_spent, 0)) AS remaining_amount
    FROM
        budget_tracking bt
    JOIN
        user_categories uc ON bt.category_id = uc.id
    LEFT JOIN
        (SELECT category_id, SUM(amount) as total_spent
         FROM transactions
         WHERE user_id = p_user_id
           AND transaction_date >= v_start_date
           AND transaction_date <= v_end_date
           AND amount > 0
         GROUP BY category_id) s ON bt.category_id = s.category_id
    WHERE
        bt.budget_id = v_budget_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_category_trend(p_user_id UUID, p_category_id UUID)
RETURNS TABLE(month TEXT, total_spent NUMERIC) AS $$
BEGIN
    RETURN QUERY
    WITH months AS (
        SELECT date_trunc('month', generate_series(CURRENT_DATE - interval '5 months', CURRENT_DATE, '1 month'))::date AS month_start
    )
    SELECT
        to_char(m.month_start, 'YYYY-MM') AS month,
        COALESCE(SUM(t.amount), 0) AS total_spent
    FROM
        months m
    LEFT JOIN
        transactions t ON date_trunc('month', t.transaction_date)::date = m.month_start
        AND t.user_id = p_user_id
        AND t.user_category_id = p_category_id
        AND t.amount > 0
    GROUP BY
        m.month_start
    ORDER BY
        m.month_start;
END;
$$ LANGUAGE plpgsql;;
