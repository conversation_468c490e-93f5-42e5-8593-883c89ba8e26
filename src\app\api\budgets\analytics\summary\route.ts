import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { BudgetError, getBudgetSummary } from '@/lib/services/budgetService';

export async function GET(request: NextRequest) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const month = searchParams.get('month');

  if (!month || !/^\d{4}-\d{2}$/.test(month)) {
    return NextResponse.json(
      { error: 'Month parameter is required in YYYY-MM format' },
      { status: 400 }
    );
  }

  try {
    const summary = await getBudgetSummary(supabase, user.id, month);
    if (!summary) {
      return NextResponse.json(
        { error: 'No budget found for the specified month' },
        { status: 404 }
      );
    }
    return NextResponse.json(summary);
  } catch (error) {
    if (error instanceof BudgetError) {
      return NextResponse.json(
        { error: error.message, details: error.details },
        { status: error.status }
      );
    }
    console.error('Error fetching budget summary:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
