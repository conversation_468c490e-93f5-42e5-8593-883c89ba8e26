{"name": "navsync", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit --pretty", "type-check:project": "tsc --noEmit --pretty --project tsconfig.json", "audit": "npm audit --audit-level=moderate", "prepare": "husky", "format": "prettier --write . --ignore-path .gitignore", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci", "semantic-release": "semantic-release", "semantic-release:dry-run": "semantic-release --dry-run", "check:all": "npm run type-check && npm run lint && npm test", "supabase": "supabase"}, "dependencies": {"@babel/core": "^7.23.0", "@babel/generator": "^7.23.0", "@babel/runtime": "^7.23.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-table": "^8.21.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "^15.3.2", "plaid": "^35.0.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-plaid-link": "^4.0.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.3", "@semantic-release/npm": "^12.0.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lint-staged": "^16.0.0", "postcss": "^8", "prettier": "^3.5.3", "semantic-release": "^24.2.5", "supabase": "^2.26.9", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css,html,yml,yaml}": ["prettier --write"]}}