'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import {
  X,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  DollarSign,
  Target,
  PieChart,
  Zap,
} from 'lucide-react';
import { useCategories } from '@/lib/hooks/useCategories';
import { BudgetSummary } from './BudgetSummary';
import { toast } from 'sonner';

interface CategoryRecommendation {
  category_id: string;
  category_name: string;
  average_monthly_spending: number;
  recommended_budget: number;
}

interface RecommendationsResponse {
  success: boolean;
  data: {
    recommendations: CategoryRecommendation[];
    analysis_period_months: number;
    categories_analyzed: number;
    total_recommended_budget: number;
  };
}

interface RecommendationsErrorResponse {
  error: string;
  message?: string;
  hasData?: boolean;
}

const step1Schema = z.object({
  name: z.string().min(1, 'Budget name is required'),
  totalIncome: z.number().min(0.01, 'Income must be greater than 0'),
});

const step2Schema = z.object({
  selectedCategories: z.array(z.string()).min(1, 'Select at least one category'),
});

const step3Schema = z.object({
  allocations: z.record(z.number().min(0.01, 'Amount must be greater than 0')),
});

type Step1Data = z.infer<typeof step1Schema>;
type Step2Data = z.infer<typeof step2Schema>;
type Step3Data = z.infer<typeof step3Schema>;

interface BudgetWizardProps {
  month: string;
  onComplete: () => void;
  onCancel: () => void;
}

export function BudgetWizard({ month, onComplete, onCancel }: BudgetWizardProps) {
  const { categories, isLoading: categoriesLoading } = useCategories();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);

  // Form data for each step
  const [step1Data, setStep1Data] = useState<Step1Data | null>(null);
  const [step2Data, setStep2Data] = useState<Step2Data | null>(null);
  const [step3Data, setStep3Data] = useState<Step3Data | null>(null);

  const step1Form = useForm<Step1Data>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      name: `Budget for ${formatMonthYear(month)}`,
      totalIncome: 0,
    },
  });

  const step2Form = useForm<Step2Data>({
    resolver: zodResolver(step2Schema),
    defaultValues: {
      selectedCategories: [],
    },
  });

  const step3Form = useForm<Step3Data>({
    resolver: zodResolver(step3Schema),
    defaultValues: {
      allocations: {},
    },
  });

  function formatMonthYear(monthStr: string) {
    const [year, month] = monthStr.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  }

  const handleGetAIRecommendations = async () => {
    try {
      setIsLoadingRecommendations(true);
      setError(null);

      const response = await fetch('/api/budgets/recommendations?months=3');

      if (!response.ok) {
        const errorData: RecommendationsErrorResponse = await response.json();
        if (errorData.hasData === false) {
          // User doesn't have enough data
          toast.error(errorData.message || 'Insufficient transaction data for recommendations');
          return;
        }
        throw new Error(errorData.error || 'Failed to get recommendations');
      }

      const data: RecommendationsResponse = await response.json();

      const { recommendations } = data.data;

      if (recommendations.length === 0) {
        toast.error('No spending patterns found. Please categorize your transactions first.');
        return;
      }

      // Pre-populate Step 2 with categories that have recommendations
      const recommendedCategoryIds = recommendations.map((rec) => rec.category_id);
      step2Form.setValue('selectedCategories', recommendedCategoryIds);

      // Pre-populate Step 3 with recommended amounts
      const recommendedAllocations: Record<string, number> = {};
      recommendations.forEach((rec) => {
        recommendedAllocations[rec.category_id] = rec.recommended_budget;
      });
      step3Form.setValue('allocations', recommendedAllocations);

      // Show success message
      toast.success(
        `Applied AI recommendations for ${recommendations.length} categories based on your spending history!`
      );

      // Advance to step 2 automatically
      const currentFormData = step1Form.getValues();
      setStep1Data(currentFormData);
      setCurrentStep(2);
    } catch (error) {
      console.error('Error getting AI recommendations:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to get AI recommendations');
    } finally {
      setIsLoadingRecommendations(false);
    }
  };

  const handleStep1Submit = (data: Step1Data) => {
    setStep1Data(data);
    setCurrentStep(2);
  };

  const handleStep2Submit = (data: Step2Data) => {
    setStep2Data(data);
    // Initialize allocations for selected categories
    const initialAllocations: Record<string, number> = {};
    data.selectedCategories.forEach((categoryId) => {
      initialAllocations[categoryId] = 0;
    });
    step3Form.setValue('allocations', initialAllocations);
    setCurrentStep(3);
  };

  const handleStep3Submit = async (data: Step3Data) => {
    if (!step1Data || !step2Data) return;

    try {
      setIsSubmitting(true);
      setError(null);

      const [year, monthNum] = month.split('-').map(Number);
      const startDate = new Date(year, monthNum - 1, 1).toISOString();
      const endDate = new Date(year, monthNum, 0, 23, 59, 59).toISOString();

      const items = step2Data.selectedCategories.map((categoryId) => ({
        categoryId,
        allocatedAmount: data.allocations[categoryId] || 0,
      }));

      const payload = {
        name: step1Data.name,
        startDate,
        endDate,
        totalIncome: step1Data.totalIncome,
        items,
      };

      const response = await fetch('/api/budgets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create budget');
      }

      setStep3Data(data);
      setCurrentStep(4); // Success step
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create budget');
    } finally {
      setIsSubmitting(false);
    }
  };

  const goBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getTotalAllocated = () => {
    if (!step3Data) return 0;
    return Object.values(step3Data.allocations).reduce((sum, amount) => sum + amount, 0);
  };

  const getSelectedCategoriesData = () => {
    if (!step2Data) return [];
    return categories.filter((cat) => step2Data.selectedCategories.includes(cat.id));
  };

  if (categoriesLoading) {
    return (
      <div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='flex items-center justify-center py-8'>
            <LoadingSpinner />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4'>
      <Card className='w-full max-w-2xl max-h-[90vh] overflow-hidden'>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <div>
            <CardTitle>Budget Setup Wizard</CardTitle>
            <CardDescription>
              Step {currentStep} of 4 - Let&apos;s create your budget together
            </CardDescription>
          </div>
          <Button variant='ghost' size='sm' onClick={onCancel}>
            <X className='h-4 w-4' />
          </Button>
        </CardHeader>

        <CardContent className='overflow-y-auto max-h-[calc(90vh-120px)]'>
          {/* Progress Indicator */}
          <div className='flex items-center justify-center mb-8'>
            <div className='flex items-center space-x-4'>
              {[1, 2, 3, 4].map((step) => (
                <div key={step} className='flex items-center'>
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      step <= currentStep
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-muted-foreground'
                    }`}
                  >
                    {step < currentStep ? <CheckCircle className='h-4 w-4' /> : step}
                  </div>
                  {step < 4 && (
                    <div
                      className={`w-12 h-0.5 ${step < currentStep ? 'bg-primary' : 'bg-muted'}`}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>

          {error && (
            <div className='mb-6 p-4 border border-destructive rounded-md bg-destructive/10'>
              <p className='text-destructive text-sm'>{error}</p>
            </div>
          )}

          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className='space-y-6'>
              <div className='text-center'>
                <DollarSign className='h-12 w-12 mx-auto text-primary mb-4' />
                <h3 className='text-xl font-semibold mb-2'>Let&apos;s start with the basics</h3>
                <p className='text-muted-foreground'>
                  Give your budget a name and tell us your monthly income
                </p>
              </div>

              <Form {...step1Form}>
                <form onSubmit={step1Form.handleSubmit(handleStep1Submit)} className='space-y-4'>
                  <FormField
                    control={step1Form.control}
                    name='name'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget Name</FormLabel>
                        <FormControl>
                          <Input placeholder='Enter budget name' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={step1Form.control}
                    name='totalIncome'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Monthly Income</FormLabel>
                        <FormControl>
                          <Input
                            type='number'
                            step='0.01'
                            placeholder='Enter your monthly income'
                            {...field}
                            onChange={(e) =>
                              field.onChange(e.target.value ? parseFloat(e.target.value) : 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className='flex flex-col gap-3 pt-4'>
                    {/* AI Recommendation Button */}
                    <div className='p-4 border border-dashed border-primary/30 rounded-lg bg-primary/5'>
                      <div className='text-center'>
                        <Zap className='h-8 w-8 mx-auto text-primary mb-2' />
                        <h4 className='font-medium text-sm mb-1'>Get AI-Powered Recommendations</h4>
                        <p className='text-xs text-muted-foreground mb-3'>
                          Let AI analyze your spending history and suggest budget amounts
                        </p>
                        <Button
                          type='button'
                          variant='outline'
                          size='sm'
                          onClick={handleGetAIRecommendations}
                          disabled={isLoadingRecommendations || !step1Form.formState.isValid}
                          className='w-full'
                        >
                          {isLoadingRecommendations && <LoadingSpinner className='mr-2 h-4 w-4' />}
                          <Zap className='mr-2 h-4 w-4' />
                          {isLoadingRecommendations ? 'Analyzing...' : 'Get Smart Recommendations'}
                        </Button>
                      </div>
                    </div>

                    {/* Manual Continue Button */}
                    <div className='flex justify-between'>
                      <span className='text-xs text-muted-foreground flex items-center'>
                        or continue manually
                      </span>
                      <Button type='submit'>
                        Next <ArrowRight className='h-4 w-4 ml-2' />
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          )}

          {/* Step 2: Category Selection */}
          {currentStep === 2 && (
            <div className='space-y-6'>
              <div className='text-center'>
                <Target className='h-12 w-12 mx-auto text-primary mb-4' />
                <h3 className='text-xl font-semibold mb-2'>Choose your categories</h3>
                <p className='text-muted-foreground'>
                  Select the categories you want to budget for this month
                </p>
              </div>

              <Form {...step2Form}>
                <form onSubmit={step2Form.handleSubmit(handleStep2Submit)} className='space-y-4'>
                  <FormField
                    control={step2Form.control}
                    name='selectedCategories'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Categories</FormLabel>
                        <div className='grid grid-cols-2 gap-3'>
                          {categories.map((category) => (
                            <label
                              key={category.id}
                              className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                                field.value.includes(category.id)
                                  ? 'border-primary bg-primary/5'
                                  : 'border-border hover:border-primary/50'
                              }`}
                            >
                              <input
                                type='checkbox'
                                checked={field.value.includes(category.id)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    field.onChange([...field.value, category.id]);
                                  } else {
                                    field.onChange(field.value.filter((id) => id !== category.id));
                                  }
                                }}
                                className='sr-only'
                              />
                              <div className='flex items-center gap-2'>
                                {category.icon && <span className='text-lg'>{category.icon}</span>}
                                <span className='text-sm font-medium'>{category.name}</span>
                              </div>
                            </label>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className='flex justify-between pt-4'>
                    <Button type='button' variant='outline' onClick={goBack}>
                      <ArrowLeft className='h-4 w-4 mr-2' /> Back
                    </Button>
                    <Button type='submit'>
                      Next <ArrowRight className='h-4 w-4 ml-2' />
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          )}

          {/* Step 3: Allocate Amounts */}
          {currentStep === 3 && step1Data && step2Data && (
            <div className='space-y-6'>
              <div className='text-center'>
                <PieChart className='h-12 w-12 mx-auto text-primary mb-4' />
                <h3 className='text-xl font-semibold mb-2'>Allocate your budget</h3>
                <p className='text-muted-foreground'>
                  Set how much you want to spend in each category
                </p>
              </div>

              <BudgetSummary income={step1Data.totalIncome} allocated={getTotalAllocated()} />

              <Form {...step3Form}>
                <form onSubmit={step3Form.handleSubmit(handleStep3Submit)} className='space-y-4'>
                  <div className='space-y-4'>
                    {getSelectedCategoriesData().map((category) => (
                      <FormField
                        key={category.id}
                        control={step3Form.control}
                        name={`allocations.${category.id}`}
                        render={({ field }) => (
                          <FormItem>
                            <div className='flex items-center justify-between'>
                              <FormLabel className='flex items-center gap-2'>
                                {category.icon && <span className='text-lg'>{category.icon}</span>}
                                {category.name}
                              </FormLabel>
                            </div>
                            <FormControl>
                              <Input
                                type='number'
                                step='0.01'
                                min='0'
                                placeholder='0.00'
                                {...field}
                                onChange={(e) =>
                                  field.onChange(e.target.value ? parseFloat(e.target.value) : 0)
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>

                  <div className='flex justify-between pt-4'>
                    <Button type='button' variant='outline' onClick={goBack}>
                      <ArrowLeft className='h-4 w-4 mr-2' /> Back
                    </Button>
                    <Button type='submit' disabled={isSubmitting}>
                      {isSubmitting && <LoadingSpinner className='mr-2 h-4 w-4' />}
                      Create Budget
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          )}

          {/* Step 4: Success */}
          {currentStep === 4 && (
            <div className='text-center space-y-6'>
              <CheckCircle className='h-16 w-16 mx-auto text-green-500' />
              <div>
                <h3 className='text-xl font-semibold mb-2'>Budget Created Successfully!</h3>
                <p className='text-muted-foreground'>
                  Your budget for {formatMonthYear(month)} is ready to use.
                </p>
              </div>
              <Button onClick={onComplete} className='w-full'>
                View My Budget
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
