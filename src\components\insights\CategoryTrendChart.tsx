'use client';

import { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartData,
} from 'chart.js';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CategorySelector from '@/components/categories/CategorySelector';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

interface TrendData {
  month: string;
  totalSpent: number;
}

interface CategoryTrendResponse {
  data: TrendData[];
  metadata: {
    categoryId: string;
    categoryName: string;
    currency: string;
  };
}

export const CategoryTrendChart = () => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [chartData, setChartData] = useState<ChartData<'line'> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!selectedCategoryId) {
      setChartData(null);
      return;
    }

    const fetchTrendData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(
          `/api/insights/category-trend?categoryId=${selectedCategoryId}`
        );
        if (!response.ok) {
          throw new Error('Failed to fetch category trend data');
        }
        const result: CategoryTrendResponse = await response.json();
        const trendData = result.data || [];

        const labels = trendData.map((d) => d.month);
        const values = trendData.map((d) => d.totalSpent);

        setChartData({
          labels,
          datasets: [
            {
              label: 'Spending',
              data: values,
              borderColor: 'hsl(var(--primary))',
              backgroundColor: 'hsla(var(--primary), 0.1)',
              borderWidth: 3,
              pointBackgroundColor: 'hsl(var(--primary))',
              pointBorderColor: 'hsl(var(--background))',
              pointBorderWidth: 2,
              pointRadius: 6,
              pointHoverRadius: 8,
              tension: 0.4,
              fill: true,
            },
          ],
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setChartData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrendData();
  }, [selectedCategoryId]);

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategoryId(categoryId === 'all' ? null : categoryId);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Category Spending Trend</CardTitle>
        <div className='pt-4'>
          <CategorySelector
            selectedValue={selectedCategoryId || ''}
            onValueChange={handleCategoryChange}
          />
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className='flex justify-center items-center h-64'>
            <LoadingSpinner />
          </div>
        ) : error ? (
          <div className='flex justify-center items-center h-64 text-red-500'>
            <p>{error}</p>
          </div>
        ) : chartData ? (
          <div className='relative h-64'>
            <Line
              data={chartData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: false,
                  },
                  tooltip: {
                    backgroundColor: 'hsl(var(--popover))',
                    titleColor: 'hsl(var(--popover-foreground))',
                    bodyColor: 'hsl(var(--popover-foreground))',
                    borderColor: 'hsl(var(--border))',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                      label: function (context) {
                        return `$${context.parsed.y.toFixed(2)}`;
                      },
                    },
                  },
                },
                scales: {
                  x: {
                    grid: {
                      color: 'hsl(var(--border))',
                    },
                    ticks: {
                      color: 'hsl(var(--muted-foreground))',
                    },
                    border: {
                      display: false,
                    },
                  },
                  y: {
                    grid: {
                      color: 'hsl(var(--border))',
                    },
                    ticks: {
                      color: 'hsl(var(--muted-foreground))',
                      callback: function (value) {
                        return '$' + value;
                      },
                    },
                    border: {
                      display: false,
                    },
                  },
                },
                interaction: {
                  intersect: false,
                  mode: 'index',
                },
              }}
            />
          </div>
        ) : selectedCategoryId ? (
          <div className='flex justify-center items-center h-64'>
            <p className='text-muted-foreground'>No data available for the selected category.</p>
          </div>
        ) : (
          <div className='flex justify-center items-center h-64'>
            <p className='text-muted-foreground'>
              Please select a category to view the spending trend.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
