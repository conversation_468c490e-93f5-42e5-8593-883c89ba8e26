import { createSupabaseServerClient } from '@/lib/supabase/server';
import { BudgetError, updateBudget, deleteBudget } from '@/lib/services/budgetService';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const budgetItemUpdateSchema = z.object({
  id: z.string().uuid().optional(),
  categoryId: z.string().uuid(),
  allocatedAmount: z.number().positive(),
  notes: z.string().optional().nullable(),
});

const updateBudgetSchema = z.object({
  name: z.string().min(1).optional(),
  totalIncome: z.number().optional(),
  items: z.array(budgetItemUpdateSchema).optional(),
});

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id: budgetId } = await params;
  const body = await request.json();
  const validation = updateBudgetSchema.safeParse(body);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid request body', details: validation.error.flatten() },
      { status: 400 }
    );
  }

  const { items, totalIncome, ...budgetData } = validation.data;

  // Map camelCase to snake_case for database
  const dbBudgetData = {
    ...budgetData,
    ...(totalIncome !== undefined && { total_income: totalIncome }),
  };

  const itemsUpdate = items?.map((item) => ({
    id: item.id,
    category_id: item.categoryId,
    allocated_amount: item.allocatedAmount,
    notes: item.notes,
  }));

  try {
    const updatedBudget = await updateBudget(supabase, user.id, budgetId, {
      ...dbBudgetData,
      items: itemsUpdate,
    });
    return NextResponse.json(updatedBudget);
  } catch (error) {
    if (error instanceof BudgetError) {
      return NextResponse.json(
        { error: error.message, details: error.details },
        { status: error.status }
      );
    }
    console.error(`Unhandled error in PUT /api/budgets/${budgetId}:`, error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id: budgetId } = await params;

  try {
    const result = await deleteBudget(supabase, user.id, budgetId);
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    if (error instanceof BudgetError) {
      return NextResponse.json(
        { error: error.message, details: error.details },
        { status: error.status }
      );
    }
    console.error(`Unhandled error in DELETE /api/budgets/${budgetId}:`, error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
