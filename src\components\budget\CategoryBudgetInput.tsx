'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Category } from '@/lib/hooks/useCategories';

export interface CategoryBudgetInputValue {
  categoryId: string;
  allocatedAmount: number;
  notes: string;
}

export interface CategoryBudgetInputError {
  categoryId?: string;
  allocatedAmount?: string;
}

interface CategoryBudgetInputProps {
  categories: Category[];
  value: CategoryBudgetInputValue;
  onChange: (value: CategoryBudgetInputValue) => void;
  error?: CategoryBudgetInputError;
}

export function CategoryBudgetInput({
  categories,
  value,
  onChange,
  error,
}: CategoryBudgetInputProps) {
  const handleCategoryChange = (categoryId: string) => {
    onChange({ ...value, categoryId });
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const amount = e.target.value ? parseFloat(e.target.value) : 0;
    onChange({ ...value, allocatedAmount: amount });
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ ...value, notes: e.target.value });
  };

  const selectedCategory = categories.find((cat) => cat.id === value.categoryId);

  return (
    <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
      {/* Category Selection */}
      <div className='space-y-2'>
        <Label htmlFor='category'>Category</Label>
        <Select value={value.categoryId} onValueChange={handleCategoryChange}>
          <SelectTrigger className={error?.categoryId ? 'border-destructive' : ''}>
            <SelectValue placeholder='Select category'>
              {selectedCategory && (
                <div className='flex items-center gap-2'>
                  {selectedCategory.icon && (
                    <span className='text-sm'>{selectedCategory.icon}</span>
                  )}
                  <span>{selectedCategory.name}</span>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                <div className='flex items-center gap-2'>
                  {category.icon && <span className='text-sm'>{category.icon}</span>}
                  <span>{category.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {error?.categoryId && <p className='text-sm text-destructive'>{error.categoryId}</p>}
      </div>

      {/* Allocated Amount */}
      <div className='space-y-2'>
        <Label htmlFor='amount'>Allocated Amount</Label>
        <Input
          id='amount'
          type='number'
          step='0.01'
          min='0'
          placeholder='0.00'
          value={value.allocatedAmount?.toString() || ''}
          onChange={handleAmountChange}
          className={error?.allocatedAmount ? 'border-destructive' : ''}
        />
        {error?.allocatedAmount && (
          <p className='text-sm text-destructive'>{error.allocatedAmount}</p>
        )}
      </div>

      {/* Notes */}
      <div className='space-y-2'>
        <Label htmlFor='notes'>Notes (Optional)</Label>
        <Input
          id='notes'
          placeholder='Add notes...'
          value={value.notes || ''}
          onChange={handleNotesChange}
        />
      </div>
    </div>
  );
}
