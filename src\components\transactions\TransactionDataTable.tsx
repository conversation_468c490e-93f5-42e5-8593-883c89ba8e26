'use client';

import React from 'react';
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
  RowSelectionState,
} from '@tanstack/react-table';
import { Search, Download, Tag, Plus, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Transaction } from './TransactionCard';
import { createColumns } from './columns';

interface Category {
  id: string;
  name: string;
}

interface TransactionDataTableProps {
  data: Transaction[];
  categories: Category[];
  isLoading?: boolean;
  isLoadingMore?: boolean;
  isSearchingAll?: boolean;
  hasMorePages?: boolean;
  totalCount?: number;
  clientSearchQuery?: string;
  isSearchingAllData?: boolean;
  onTransactionClick: (transaction: Transaction) => void;
  onBulkCategorize: (transactionIds: string[]) => void;
  onLoadMore?: () => void;
  onSearchChange?: (query: string) => void;
  onSearchAll?: (query: string) => void;
  onResetView?: () => void;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  onOptimisticCategoryUpdate?: (transactionId: string, categoryId: string) => void;
}

export default function TransactionDataTable({
  data,
  categories,
  isLoading = false,
  isLoadingMore = false,
  isSearchingAll = false,
  hasMorePages = false,
  totalCount = 0,
  clientSearchQuery = '',
  isSearchingAllData = false,
  onTransactionClick,
  onBulkCategorize,
  onLoadMore,
  onSearchChange,
  onSearchAll,
  onResetView,
  onSort,
  onOptimisticCategoryUpdate,
}: TransactionDataTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({});
  const [showUncategorizedFirst, setShowUncategorizedFirst] = React.useState(false);

  // Transform data for uncategorized-first sorting
  const transformedData = React.useMemo(() => {
    if (!showUncategorizedFirst) return data;

    return [...data].sort((a, b) => {
      const aUncategorized = !a.user_category_id;
      const bUncategorized = !b.user_category_id;

      if (aUncategorized && !bUncategorized) return -1;
      if (!aUncategorized && bUncategorized) return 1;

      // If both are categorized or both uncategorized, sort by date (most recent first)
      return new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime();
    });
  }, [data, showUncategorizedFirst]);

  const columns = React.useMemo(
    () =>
      createColumns({
        onTransactionClick,
        categories,
        onSort,
        onOptimisticUpdate: onOptimisticCategoryUpdate,
      }),
    [onTransactionClick, categories, onSort, onOptimisticCategoryUpdate]
  );

  const table = useReactTable({
    data: transformedData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    manualSorting: true, // We handle sorting via onSort callback
  });

  // Export functionality
  const handleExportCSV = () => {
    const csvData = table.getFilteredRowModel().rows.map((row) => {
      const transaction = row.original;
      return {
        Date: new Date(transaction.transaction_date).toLocaleDateString(),
        Merchant: transaction.merchant_name || transaction.description,
        Amount: transaction.amount,
        Category: transaction.user_category_id || 'Uncategorized',
        Account: transaction.financial_accounts.account_name,
        Status: transaction.is_pending ? 'Pending' : 'Posted',
      };
    });

    const csv = [
      Object.keys(csvData[0] || {}).join(','),
      ...csvData.map((row) => Object.values(row).join(',')),
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transactions-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Handle bulk categorize
  const handleBulkCategorizeClick = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const transactionIds = selectedRows.map((row) => row.original.id);

    if (transactionIds.length === 0) {
      toast.error('Please select transactions to categorize');
      return;
    }

    onBulkCategorize(transactionIds);
  };

  const selectedRowCount = table.getFilteredSelectedRowModel().rows.length;

  if (isLoading) {
    return (
      <div className='space-y-4'>
        {/* Header skeleton */}
        <div className='flex items-center justify-between'>
          <Skeleton className='h-9 w-64' />
          <div className='flex space-x-2'>
            <Skeleton className='h-9 w-20' />
            <Skeleton className='h-9 w-20' />
          </div>
        </div>

        {/* Table skeleton */}
        <div className='rounded-md border'>
          <div className='border-b bg-gray-50/50'>
            <div className='flex'>
              {Array.from({ length: 7 }).map((_, i) => (
                <div key={i} className='flex-1 p-3'>
                  <Skeleton className='h-4 w-full' />
                </div>
              ))}
            </div>
          </div>
          {Array.from({ length: 10 }).map((_, i) => (
            <div key={i} className='border-b last:border-0'>
              <div className='flex'>
                {Array.from({ length: 7 }).map((_, j) => (
                  <div key={j} className='flex-1 p-3'>
                    <Skeleton className='h-4 w-full' />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      {/* Search and Actions Bar */}
      <div className='flex flex-col space-y-3'>
        {/* Top row: Search and Quick Actions */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            {/* Search Input with Search All Option */}
            <div className='flex items-center space-x-2'>
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400' />
                <Input
                  placeholder='Search loaded transactions...'
                  value={clientSearchQuery}
                  onChange={(e) => onSearchChange?.(e.target.value)}
                  className='pl-10 w-72 h-9 border-gray-200 focus:border-indigo-500 focus:ring-indigo-500 transition-colors'
                  disabled={isSearchingAll}
                />
              </div>

              {clientSearchQuery && (
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onSearchAll?.(clientSearchQuery)}
                  disabled={isSearchingAll}
                  className='border-gray-200 hover:bg-gray-50 whitespace-nowrap'
                >
                  <Globe className='mr-2 h-4 w-4' />
                  {isSearchingAll ? 'Searching...' : 'Search All'}
                </Button>
              )}

              {isSearchingAllData && (
                <Button
                  variant='outline'
                  size='sm'
                  onClick={onResetView}
                  className='border-gray-200 hover:bg-gray-50'
                >
                  Reset View
                </Button>
              )}
            </div>

            {/* Quick Filter: Uncategorized First */}
            <Button
              variant={showUncategorizedFirst ? 'default' : 'outline'}
              size='sm'
              onClick={() => setShowUncategorizedFirst(!showUncategorizedFirst)}
              className={
                showUncategorizedFirst
                  ? 'bg-orange-600 hover:bg-orange-700 text-white'
                  : 'border-gray-200 hover:bg-gray-50'
              }
            >
              <Tag className='mr-2 h-4 w-4' />
              Uncategorized First
            </Button>
          </div>

          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={handleExportCSV}
              className='border-gray-200 hover:bg-gray-50'
            >
              <Download className='mr-2 h-4 w-4' />
              Export CSV
            </Button>
          </div>
        </div>

        {/* Bottom row: Selection Actions (when items selected) */}
        {selectedRowCount > 0 && (
          <div className='flex items-center justify-between bg-indigo-50 px-4 py-2 rounded-lg border border-indigo-200'>
            <span className='text-sm text-indigo-700 font-medium'>
              {selectedRowCount} transaction{selectedRowCount > 1 ? 's' : ''} selected
            </span>
            <Button
              onClick={handleBulkCategorizeClick}
              size='sm'
              className='bg-indigo-600 hover:bg-indigo-700'
            >
              Categorize Selected
            </Button>
          </div>
        )}
      </div>

      {/* Results Summary */}
      <div className='text-sm text-gray-600'>
        {isSearchingAllData ? (
          <>Found {data.length} transactions across all data</>
        ) : clientSearchQuery ? (
          <>
            Found {table.getFilteredRowModel().rows.length} of {data.length} loaded transactions
          </>
        ) : (
          <>
            Showing {data.length} of {totalCount} total transactions
          </>
        )}
      </div>

      {/* Table */}
      <div className='rounded-lg border border-gray-200 bg-white shadow-sm overflow-hidden'>
        <Table>
          <TableHeader className='bg-gradient-to-r from-gray-50 to-gray-100'>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                key={headerGroup.id}
                className='hover:bg-transparent border-b border-gray-200'
              >
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className='font-semibold text-gray-700 text-xs uppercase tracking-wider py-3 px-3'
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className={`
                    cursor-pointer border-b border-gray-100 transition-all duration-200
                    ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'}
                    hover:bg-indigo-50/50 hover:shadow-sm
                    ${row.getIsSelected() ? 'bg-indigo-100/50 ring-1 ring-indigo-200' : ''}
                  `}
                  onClick={() => {
                    if (!row.getIsSelected()) {
                      onTransactionClick(row.original);
                    }
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className='py-2.5 px-3 text-sm'>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center text-gray-500 bg-gray-50/30'
                >
                  {clientSearchQuery
                    ? 'No transactions match your search.'
                    : 'No transactions found.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Load More Section */}
      {hasMorePages && !isSearchingAllData && (
        <div className='flex justify-center py-4'>
          <Button
            variant='outline'
            onClick={onLoadMore}
            disabled={isLoadingMore}
            className='border-gray-200 hover:bg-gray-50'
          >
            <Plus className='mr-2 h-4 w-4' />
            {isLoadingMore ? 'Loading...' : 'Load More Transactions'}
          </Button>
        </div>
      )}

      {/* Table Footer Info */}
      <div className='flex items-center justify-between text-sm text-gray-600'>
        <div>
          {selectedRowCount > 0 && (
            <span>
              {selectedRowCount} of {table.getFilteredRowModel().rows.length} row(s) selected.
            </span>
          )}
        </div>
        <div className='flex items-center space-x-2'>
          <span>
            {isSearchingAllData
              ? 'All matching transactions shown'
              : `${table.getFilteredRowModel().rows.length} transactions loaded`}
          </span>
        </div>
      </div>
    </div>
  );
}
