-- Clean up legacy access tokens from JSON metadata
-- Remove access_token from plaid_metadata for accounts that have been migrated to Vault

UPDATE financial_accounts 
SET plaid_metadata = plaid_metadata::jsonb - 'access_token'
WHERE plaid_access_token_vault_id IS NOT NULL 
AND plaid_metadata IS NOT NULL 
AND plaid_metadata::jsonb ? 'access_token';

-- Log the cleanup
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Cleaned up access tokens from % financial account records', updated_count;
END $$;