-- Fix ambiguous column reference in get_budget_vs_actual function

DROP FUNCTION IF EXISTS get_budget_vs_actual(UUID, TEXT);

CREATE OR REPLACE FUNCTION get_budget_vs_actual(p_user_id UUID, p_period TEXT)
RETURNS TABLE(
    category_id UUID,
    category_name TEXT,
    allocated_amount NUMERIC,
    spent_amount NUMERIC,
    remaining_amount NUMERIC
) AS $$
DECLARE
    v_budget_id UUID;
    v_start_date DATE;
    v_end_date DATE;
BEGIN
    -- Determine date range based on period parameter
    CASE p_period
        WHEN 'current_month' THEN
            v_start_date := DATE_TRUNC('month', CURRENT_DATE);
            v_end_date := DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day';
        WHEN 'last_month' THEN
            v_start_date := DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month');
            v_end_date := DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 day';
        WHEN 'this_year' THEN
            v_start_date := DATE_TRUNC('year', CURRENT_DATE);
            v_end_date := DATE_TRUNC('year', CURRENT_DATE) + INTERVAL '1 year' - INTERVAL '1 day';
        ELSE
            -- Default to current month
            v_start_date := DATE_TRUNC('month', CURRENT_DATE);
            v_end_date := DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day';
    END CASE;

    -- Find active budget that overlaps with the requested period
    SELECT id INTO v_budget_id
    FROM budgets
    WHERE user_id = p_user_id 
      AND is_active = TRUE
      AND start_date <= v_end_date
      AND end_date >= v_start_date
    ORDER BY created_at DESC
    LIMIT 1;

    -- If no budget found, return empty result
    IF v_budget_id IS NULL THEN
        RETURN;
    END IF;

    -- Return budget vs actual comparison
    RETURN QUERY
    SELECT
        bi.category_id,
        uc.name AS category_name,
        bi.allocated_amount,
        COALESCE(s.total_spent, 0) AS spent_amount,
        (bi.allocated_amount - COALESCE(s.total_spent, 0)) AS remaining_amount
    FROM
        budget_items bi
    JOIN
        user_categories uc ON bi.category_id = uc.id
    LEFT JOIN
        (SELECT t.category_id, SUM(ABS(t.amount)) as total_spent
         FROM transactions t
         WHERE t.user_id = p_user_id
           AND t.transaction_date >= v_start_date
           AND t.transaction_date <= v_end_date
           AND t.amount < 0  -- Only count expenses (negative amounts)
         GROUP BY t.category_id) s ON bi.category_id = s.category_id
    WHERE
        bi.budget_id = v_budget_id;
END;
$$ LANGUAGE plpgsql;