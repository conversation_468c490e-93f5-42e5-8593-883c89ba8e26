'use client';

import React from 'react';
import { useIsDesktop } from '@/lib/hooks/useMediaQuery';
import { useTransactionsEnhanced } from '@/lib/hooks/useTransactionsEnhanced';
import TransactionDataTable from './TransactionDataTable';
import TransactionCard, { Transaction } from './TransactionCard';

interface ResponsiveTransactionViewProps {
  // Mobile props (passed down from TransactionsList)
  transactions: Transaction[];
  isLoading: boolean;
  error: string | null;
  selectedTransactionIds: string[];
  onTransactionClick: (transaction: Transaction) => void;
  onToggleSelected: (transactionId: string) => void;
  onBulkCategorize: (transactionIds: string[]) => void;
}

/**
 * ResponsiveTransactionView component switches between:
 * - Desktop (>= 1024px): TransactionDataTable with enhanced features (uses own data loading)
 * - Mobile/Tablet (< 1024px): TransactionCard list view (uses passed props)
 */
export default function ResponsiveTransactionView({
  transactions,
  isLoading,
  error,
  selectedTransactionIds,
  onTransactionClick,
  onToggleSelected,
  onBulkCategorize,
}: ResponsiveTransactionViewProps) {
  const isDesktop = useIsDesktop();

  // Enhanced hook for desktop data table (only enabled on desktop)
  const {
    transactions: enhancedTransactions,
    categories,
    isLoading: enhancedIsLoading,
    isLoadingMore,
    isSearchingAll,
    error: enhancedError,
    hasMorePages,
    loadMoreTransactions,
    clientSearchQuery,
    setClientSearchQuery,
    searchAllTransactions,
    isSearchingAllData,
    resetToInitialView,
    handleSort,
    totalCount,
    retryFetch,
    updateTransactionCategory,
  } = useTransactionsEnhanced(isDesktop);

  // Error state
  if (error) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <div className='bg-red-50 border border-red-200 rounded-lg p-6 max-w-md'>
          <h3 className='text-red-800 font-semibold mb-2'>Error Loading Transactions</h3>
          <p className='text-red-600 mb-4'>{error}</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!isLoading && transactions.length === 0) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <div className='bg-gray-50 border border-gray-200 rounded-lg p-6 max-w-md text-center'>
          <h3 className='text-gray-800 font-semibold mb-2'>No Transactions Found</h3>
          <p className='text-gray-600'>
            You don&apos;t have any transactions yet. Connect your bank accounts to start importing
            transactions.
          </p>
        </div>
      </div>
    );
  }

  // Desktop: Use enhanced data table with its own data loading
  if (isDesktop) {
    // Desktop error state
    if (enhancedError) {
      return (
        <div className='flex flex-col items-center justify-center py-12'>
          <div className='bg-red-50 border border-red-200 rounded-lg p-6 max-w-md'>
            <h3 className='text-red-800 font-semibold mb-2'>Error Loading Transactions</h3>
            <p className='text-red-600 mb-4'>{enhancedError}</p>
            <button
              onClick={retryFetch}
              className='px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700'
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    // Desktop empty state
    if (!enhancedIsLoading && enhancedTransactions.length === 0) {
      return (
        <div className='flex flex-col items-center justify-center py-12'>
          <div className='bg-gray-50 border border-gray-200 rounded-lg p-6 max-w-md text-center'>
            <h3 className='text-gray-800 font-semibold mb-2'>No Transactions Found</h3>
            <p className='text-gray-600'>
              You don&apos;t have any transactions yet. Connect your bank accounts to start
              importing transactions.
            </p>
          </div>
        </div>
      );
    }

    return (
      <TransactionDataTable
        data={enhancedTransactions}
        categories={categories}
        isLoading={enhancedIsLoading}
        isLoadingMore={isLoadingMore}
        isSearchingAll={isSearchingAll}
        hasMorePages={hasMorePages}
        totalCount={totalCount}
        clientSearchQuery={clientSearchQuery}
        isSearchingAllData={isSearchingAllData}
        onTransactionClick={onTransactionClick}
        onBulkCategorize={onBulkCategorize}
        onLoadMore={loadMoreTransactions}
        onSearchChange={setClientSearchQuery}
        onSearchAll={searchAllTransactions}
        onResetView={resetToInitialView}
        onSort={handleSort}
        onOptimisticCategoryUpdate={updateTransactionCategory}
      />
    );
  }

  // Mobile/Tablet: Use card view
  return (
    <div className='space-y-4'>
      {/* Mobile bulk actions */}
      {selectedTransactionIds.length > 0 && (
        <div className='bg-indigo-50 p-4 rounded-lg border border-indigo-200 flex items-center justify-between'>
          <span className='text-sm font-medium text-indigo-700'>
            {selectedTransactionIds.length} transaction
            {selectedTransactionIds.length > 1 ? 's' : ''} selected
          </span>
          <button
            onClick={() => onBulkCategorize(selectedTransactionIds)}
            className='px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm font-medium'
          >
            Categorize Selected
          </button>
        </div>
      )}

      {/* Transaction cards */}
      {isLoading ? (
        <div className='flex flex-col items-center justify-center py-12'>
          <div className='animate-pulse space-y-4 w-full'>
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className='bg-gray-200 h-24 rounded-lg'></div>
            ))}
          </div>
        </div>
      ) : (
        <div className='space-y-4'>
          {transactions.map((transaction) => (
            <TransactionCard
              key={transaction.id}
              transaction={transaction}
              onClick={onTransactionClick}
              isSelected={selectedTransactionIds.includes(transaction.id)}
              onToggleSelected={onToggleSelected}
            />
          ))}
        </div>
      )}
    </div>
  );
}
