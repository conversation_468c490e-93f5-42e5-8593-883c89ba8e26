'use client';

import React from 'react';
import { Transaction } from '@/components/transactions/TransactionCard';
import { useTransactions } from '@/lib/hooks/useTransactions';
import TransactionDetailModal from './TransactionDetailModal';
import CategorySelector from '@/components/categories/CategorySelector';
import BulkCategorizeModal from './BulkCategorizeModal';
import ResponsiveTransactionView from './ResponsiveTransactionView';
import { useIsDesktop } from '@/lib/hooks/useMediaQuery';

/**
 * TransactionsList component displays a paginated list of user transactions.
 * It includes filtering and searching functionality.
 */
export default function TransactionsList() {
  const [selectedTransaction, setSelectedTransaction] = React.useState<Transaction | null>(null);
  const [selectedTransactionIds, setSelectedTransactionIds] = React.useState<string[]>([]);
  const [isBulkModalOpen, setIsBulkModalOpen] = React.useState(false);
  const isDesktop = useIsDesktop();

  // Filter state (only used for mobile view)
  const [searchQuery, setSearchQuery] = React.useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = React.useState('');
  const [categoryId, setCategoryId] = React.useState('');
  const [startDate, setStartDate] = React.useState('');
  const [endDate, setEndDate] = React.useState('');

  // Debounce search query to avoid API calls on every keystroke
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Memoize filters to prevent unnecessary re-renders (only for mobile view)
  const filters = React.useMemo(() => {
    // Desktop uses its own filtering, so only apply filters on mobile
    if (isDesktop) return undefined;

    const hasFilters = debouncedSearchQuery || categoryId || startDate || endDate;
    if (!hasFilters) return undefined;

    return {
      searchQuery: debouncedSearchQuery || undefined,
      categoryId: categoryId || undefined,
      startDate: startDate || undefined,
      endDate: endDate || undefined,
    };
  }, [debouncedSearchQuery, categoryId, startDate, endDate, isDesktop]);

  // Use the hook with memoized filters
  const { transactions, pagination, isLoading, error, handlePageChange, retryFetch } =
    useTransactions(filters);

  const handleCardClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
  };

  const handleToggleSelected = (transactionId: string) => {
    setSelectedTransactionIds((prev) =>
      prev.includes(transactionId)
        ? prev.filter((id) => id !== transactionId)
        : [...prev, transactionId]
    );
  };

  const handleCloseModal = () => {
    setSelectedTransaction(null);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setCategoryId('');
    setStartDate('');
    setEndDate('');
  };

  const handleBulkCategorizeSuccess = () => {
    setSelectedTransactionIds([]);
    setIsBulkModalOpen(false);
    retryFetch(); // This will refetch the transactions
  };

  const handleBulkCategorize = (transactionIds: string[]) => {
    setSelectedTransactionIds(transactionIds);
    setIsBulkModalOpen(true);
  };

  const hasActiveFilters = searchQuery || categoryId || startDate || endDate;

  return (
    <div className='space-y-6'>
      {/* Mobile-only Filters Section */}
      {!isDesktop && (
        <div className='bg-white p-6 rounded-lg border border-gray-200'>
          <h3 className='text-lg font-semibold mb-4'>Filter Transactions</h3>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            {/* Search Input */}
            <div>
              <label htmlFor='search' className='block text-sm font-medium text-gray-700 mb-1'>
                Search Merchant
              </label>
              <input
                id='search'
                type='text'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder='Search by merchant name...'
                className='block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-indigo-500 focus:ring-indigo-500'
              />
            </div>

            {/* Category Filter */}
            <div>
              <label htmlFor='category' className='block text-sm font-medium text-gray-700 mb-1'>
                Category
              </label>
              <CategorySelector selectedValue={categoryId} onValueChange={setCategoryId} />
            </div>

            {/* Start Date */}
            <div>
              <label htmlFor='startDate' className='block text-sm font-medium text-gray-700 mb-1'>
                Start Date
              </label>
              <input
                id='startDate'
                type='date'
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className='block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-indigo-500 focus:ring-indigo-500'
              />
            </div>

            {/* End Date */}
            <div>
              <label htmlFor='endDate' className='block text-sm font-medium text-gray-700 mb-1'>
                End Date
              </label>
              <input
                id='endDate'
                type='date'
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className='block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-indigo-500 focus:ring-indigo-500'
              />
            </div>
          </div>

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <div className='mt-4'>
              <button
                onClick={handleClearFilters}
                className='px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200'
              >
                Clear All Filters
              </button>
            </div>
          )}
        </div>
      )}

      {/* Mobile-only Results Summary */}
      {!isDesktop && pagination && (
        <div className='text-sm text-gray-600'>
          {hasActiveFilters ? 'Filtered results: ' : 'Showing '}
          {pagination.totalCount} transaction{pagination.totalCount !== 1 ? 's' : ''}
          {hasActiveFilters && ' found'}
        </div>
      )}

      {/* Responsive Transaction View */}
      <ResponsiveTransactionView
        transactions={transactions}
        isLoading={isLoading}
        error={error}
        selectedTransactionIds={selectedTransactionIds}
        onTransactionClick={handleCardClick}
        onToggleSelected={handleToggleSelected}
        onBulkCategorize={handleBulkCategorize}
      />

      {/* Modals */}
      {selectedTransaction && (
        <TransactionDetailModal transaction={selectedTransaction} onClose={handleCloseModal} />
      )}

      {isBulkModalOpen && (
        <BulkCategorizeModal
          transactionIds={selectedTransactionIds}
          onClose={() => setIsBulkModalOpen(false)}
          onSuccess={handleBulkCategorizeSuccess}
        />
      )}

      {/* Mobile-only Pagination */}
      {!isDesktop && pagination && pagination.totalPages > 1 && (
        <div className='flex items-center justify-between border-t pt-6'>
          <div className='text-sm text-gray-600'>
            Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} to{' '}
            {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)} of{' '}
            {pagination.totalCount} transactions
          </div>

          <div className='flex items-center space-x-2'>
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={!pagination.hasPreviousPage}
              className='px-3 py-2 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
            >
              Previous
            </button>

            <span className='px-3 py-2 text-sm'>
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>

            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={!pagination.hasNextPage}
              className='px-3 py-2 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
