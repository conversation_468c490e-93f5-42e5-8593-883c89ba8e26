# Technical Development Plan: Category Stats and Insights

**Author:** <PERSON><PERSON>, Architect
**Date:** 2025-06-15
**Status:** DRAFT

## 1. Feature Scope (MVP)

The Minimum Viable Product for the Category Stats and Insights feature will focus on providing users with foundational, non-AI-driven statistical views of their spending.

- **Dedicated Insights Page:** A new page accessible at `/dashboard/insights` will house all the new data visualizations.
- **Spending Breakdown Chart:** A primary chart (user's choice of Pie or Bar) displaying spending distribution across all categories. This will include a time-period selector for "This Month," "Last Month," and "Last 90 Days."
- **Budget vs. Actual Table:** A data table comparing budgeted amounts against actual spending for each category for the current month.
- **Category Spending Trend Chart:** A line chart illustrating the spending trend for a single, user-selected category over the last 6 months.

## 2. Backend & Database Plan

The existing database schema is sufficient. To ensure performance and data consistency, we will implement several PostgreSQL functions to handle data aggregation.

### New Database Functions

We will create three new functions in the `public` schema.

**A. `get_category_spending(p_user_id UUID, p_start_date DATE, p_end_date DATE)`**

This function will aggregate transaction amounts by category for a given user and time period.

```sql
CREATE OR REPLACE FUNCTION get_category_spending(p_user_id UUID, p_start_date DATE, p_end_date DATE)
RETURNS TABLE(category_id UUID, category_name TEXT, total_spent NUMERIC) AS $$
BEGIN
    RETURN QUERY
    SELECT
        uc.id AS category_id,
        uc.name AS category_name,
        COALESCE(SUM(t.amount), 0) AS total_spent
    FROM
        user_categories uc
    LEFT JOIN
        transactions t ON uc.id = t.user_category_id
        AND t.user_id = p_user_id
        AND t.transaction_date >= p_start_date
        AND t.transaction_date <= p_end_date
        AND t.amount > 0 -- Only include spending, not income
    WHERE
        uc.user_id = p_user_id
    GROUP BY
        uc.id, uc.name
    ORDER BY
        total_spent DESC;
END;
$$ LANGUAGE plpgsql;
```

**B. `get_budget_vs_actual(p_user_id UUID, p_budget_period TEXT)`**

This function will retrieve budget allocations and compare them with actual spending for the specified period.

```sql
CREATE OR REPLACE FUNCTION get_budget_vs_actual(p_user_id UUID, p_budget_period TEXT)
RETURNS TABLE(
    category_id UUID,
    category_name TEXT,
    allocated_amount NUMERIC,
    spent_amount NUMERIC,
    remaining_amount NUMERIC
) AS $$
DECLARE
    v_budget_id UUID;
    v_start_date DATE;
    v_end_date DATE;
BEGIN
    -- Find the active budget for the given period
    SELECT id, start_date, end_date INTO v_budget_id, v_start_date, v_end_date
    FROM budgets
    WHERE user_id = p_user_id AND budget_period = p_budget_period AND is_active = TRUE
    LIMIT 1;

    IF v_budget_id IS NULL THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT
        bt.category_id,
        uc.name AS category_name,
        bt.allocated_amount,
        COALESCE(s.total_spent, 0) AS spent_amount,
        (bt.allocated_amount - COALESCE(s.total_spent, 0)) AS remaining_amount
    FROM
        budget_tracking bt
    JOIN
        user_categories uc ON bt.category_id = uc.id
    LEFT JOIN
        (SELECT category_id, SUM(amount) as total_spent
         FROM transactions
         WHERE user_id = p_user_id
           AND transaction_date >= v_start_date
           AND transaction_date <= v_end_date
           AND amount > 0
         GROUP BY category_id) s ON bt.category_id = s.category_id
    WHERE
        bt.budget_id = v_budget_id;
END;
$$ LANGUAGE plpgsql;
```

**C. `get_category_trend(p_user_id UUID, p_category_id UUID)`**

This function will calculate monthly spending for a specific category over the last 6 months.

```sql
CREATE OR REPLACE FUNCTION get_category_trend(p_user_id UUID, p_category_id UUID)
RETURNS TABLE(month TEXT, total_spent NUMERIC) AS $$
BEGIN
    RETURN QUERY
    WITH months AS (
        SELECT date_trunc('month', generate_series(CURRENT_DATE - interval '5 months', CURRENT_DATE, '1 month'))::date AS month_start
    )
    SELECT
        to_char(m.month_start, 'YYYY-MM') AS month,
        COALESCE(SUM(t.amount), 0) AS total_spent
    FROM
        months m
    LEFT JOIN
        transactions t ON date_trunc('month', t.transaction_date)::date = m.month_start
        AND t.user_id = p_user_id
        AND t.user_category_id = p_category_id
        AND t.amount > 0
    GROUP BY
        m.month_start
    ORDER BY
        m.month_start;
END;
$$ LANGUAGE plpgsql;
```

## 3. API Plan

We will create three new API endpoints.

**A. `GET /api/insights/spending-breakdown`**

- **Purpose:** Provides data for the category spending breakdown chart.
- **Query Parameters:**
  - `period`: `string` (Enum: `this_month`, `last_month`, `last_90_days`). Required.
- **Response Body:**
  ```json
  {
    "data": [
      {
        "categoryName": "Food & Dining",
        "totalSpent": 1250.75
      },
      {
        "categoryName": "Transportation",
        "totalSpent": 340.5
      }
    ],
    "metadata": {
      "period": "last_90_days",
      "startDate": "2025-03-17",
      "endDate": "2025-06-15",
      "currency": "USD"
    }
  }
  ```

**B. `GET /api/insights/budget-comparison`**

- **Purpose:** Provides data for the budget vs. actual spending table.
- **Query Parameters:**
  - `period`: `string` (Enum: `current_month`). Required.
- **Response Body:**
  ```json
  {
    "data": [
      {
        "categoryName": "Food & Dining",
        "allocatedAmount": 1500.0,
        "spentAmount": 1250.75,
        "remainingAmount": 249.25
      },
      {
        "categoryName": "Shopping",
        "allocatedAmount": 500.0,
        "spentAmount": 550.0,
        "remainingAmount": -50.0
      }
    ],
    "metadata": {
      "period": "current_month",
      "currency": "USD"
    }
  }
  ```

**C. `GET /api/insights/category-trend`**

- **Purpose:** Provides time-series data for a single category's spending trend.
- **Query Parameters:**
  - `categoryId`: `string` (UUID). Required.
- **Response Body:**
  ```json
  {
    "data": [
      { "month": "2025-01", "totalSpent": 450.0 },
      { "month": "2025-02", "totalSpent": 475.5 },
      { "month": "2025-03", "totalSpent": 430.25 },
      { "month": "2025-04", "totalSpent": 510.0 },
      { "month": "2025-05", "totalSpent": 490.8 },
      { "month": "2025-06", "totalSpent": 520.0 }
    ],
    "metadata": {
      "categoryId": "...",
      "categoryName": "Shopping",
      "currency": "USD"
    }
  }
  ```

## 4. Frontend Plan

- **UI Location:** A new page will be created at `/dashboard/insights`. A link to this page should be added to the main dashboard navigation.
- **Charting Library:** We will use **`Chart.js` with the `react-chartjs-2` wrapper**. This decision is based on `Chart.js`'s superior performance with large datasets and better tree-shakability, which are critical requirements for this application.
- **Styling Note:** `Chart.js` is highly customizable but does not come with default styling that matches our `shadcn/ui` aesthetic. Development time must include creating custom styles for charts to ensure visual consistency with the rest of the application.
- **New React Components:**
  - `InsightsPage.tsx`: The main container for the new page.
  - `TimePeriodSelector.tsx`: A shared component to select the time period for charts.
  - `CategoryBreakdownChart.tsx`: Displays the spending breakdown. Will be implemented using `react-chartjs-2`'s `Doughnut` or `Bar` components.
  - `BudgetComparisonTable.tsx`: Displays the budget vs. actual data. This will use our standard `shadcn/ui` Table, not a chart library component.
  - `CategoryTrendChart.tsx`: Displays the spending trend for a selected category. Will be implemented using `react-chartjs-2`'s `Line` component.
  - `CategorySelector.tsx`: A dropdown to select a category for the trend chart (can likely reuse or adapt the existing `CategorySelector`).

## 5. Implementation Task Breakdown

The work will be broken down into the following subtasks and assigned to the appropriate developer modes.

| #   | Task                                         | Description                                                                                                                                                                                            | Mode         |
| --- | -------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------------ |
| 1   | **DB:** Create Migration for Functions       | Create a new Supabase migration file and add the SQL for the three new functions (`get_category_spending`, `get_budget_vs_actual`, `get_category_trend`).                                              | **Senior**   |
| 2   | **API:** Implement `spending-breakdown`      | Create the `GET /api/insights/spending-breakdown` endpoint. It should call the `get_category_spending` DB function and format the response.                                                            | **Midlevel** |
| 3   | **API:** Implement `budget-comparison`       | Create the `GET /api/insights/budget-comparison` endpoint. It should call the `get_budget_vs_actual` DB function.                                                                                      | **Midlevel** |
| 4   | **API:** Implement `category-trend`          | Create the `GET /api/insights/category-trend` endpoint. It should call the `get_category_trend` DB function.                                                                                           | **Midlevel** |
| 5   | **FE:** Create `InsightsPage` and Navigation | Create the basic page file at `/dashboard/insights/page.tsx` and add a link to it in the main navigation component.                                                                                    | **Intern**   |
| 6   | **FE:** Build `CategoryBreakdownChart`       | Create the component to fetch data from the `spending-breakdown` API and render it using `react-chartjs-2`'s `Doughnut` or `Bar` components. Include the `TimePeriodSelector` and apply custom styles. | **Junior**   |
| 7   | **FE:** Build `BudgetComparisonTable`        | Create the component to fetch data from the `budget-comparison` API and render it using a standard `shadcn/ui` Table component.                                                                        | **Junior**   |
| 8   | **FE:** Build `CategoryTrendChart`           | Create the component to fetch data from the `category-trend` API and render it using `react-chartjs-2`'s `Line` component. Include a category selector and apply custom styles.                        | **Junior**   |
| 9   | **FE:** Assemble `InsightsPage`              | Combine all the new frontend components onto the `InsightsPage`, managing state and data fetching.                                                                                                     | **Midlevel** |
| 10  | **Test:** Write Integration Tests            | Write integration tests for the new API endpoints and frontend components to ensure they work together correctly.                                                                                      | **Midlevel** |

---

This plan provides a clear path to delivering the Category Stats and Insights feature. Once approved, we can proceed with creating the subtasks for the development team.
