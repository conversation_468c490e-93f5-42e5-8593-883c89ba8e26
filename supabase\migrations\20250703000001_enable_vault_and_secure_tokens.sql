-- Enable Supabase Vault for secure token storage
-- This migration enables Vault extension and updates schema for secure Plaid token storage

-- Enable the Vault extension
CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";

-- Add vault_secret_id column to financial_accounts table
ALTER TABLE financial_accounts 
ADD COLUMN IF NOT EXISTS plaid_access_token_vault_id UUID REFERENCES vault.secrets(id) ON DELETE SET NULL;

-- Add index for better performance on vault lookups
CREATE INDEX IF NOT EXISTS idx_financial_accounts_vault_token 
ON financial_accounts (plaid_access_token_vault_id);

-- Create secure token management functions
CREATE OR REPLACE FUNCTION store_plaid_access_token(
    p_user_id UUID,
    p_account_id UUID,
    p_access_token TEXT,
    p_description TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_secret_id UUID;
    v_secret_name TEXT;
BEGIN
    -- Create unique secret name
    v_secret_name := 'plaid_access_token_' || p_account_id::TEXT;
    
    -- Store token in Vault
    SELECT vault.create_secret(
        p_access_token,
        v_secret_name,
        COALESCE(p_description, 'Plaid access token for account ' || p_account_id::TEXT)
    ) INTO v_secret_id;
    
    -- Update financial_accounts table with vault reference
    UPDATE financial_accounts 
    SET plaid_access_token_vault_id = v_secret_id
    WHERE id = p_account_id AND user_id = p_user_id;
    
    RETURN v_secret_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to retrieve access token securely
CREATE OR REPLACE FUNCTION get_plaid_access_token(
    p_user_id UUID,
    p_account_id UUID
) RETURNS TEXT AS $$
DECLARE
    v_access_token TEXT;
    v_vault_id UUID;
BEGIN
    -- Get vault ID for the account
    SELECT plaid_access_token_vault_id INTO v_vault_id
    FROM financial_accounts
    WHERE id = p_account_id AND user_id = p_user_id;
    
    -- Return NULL if no vault ID found
    IF v_vault_id IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Retrieve decrypted token from Vault
    SELECT decrypted_secret INTO v_access_token
    FROM vault.decrypted_secrets
    WHERE id = v_vault_id;
    
    RETURN v_access_token;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update access token securely
CREATE OR REPLACE FUNCTION update_plaid_access_token(
    p_user_id UUID,
    p_account_id UUID,
    p_new_access_token TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_vault_id UUID;
    v_secret_name TEXT;
BEGIN
    -- Get current vault ID
    SELECT plaid_access_token_vault_id INTO v_vault_id
    FROM financial_accounts
    WHERE id = p_account_id AND user_id = p_user_id;
    
    -- If vault ID exists, update the existing secret
    IF v_vault_id IS NOT NULL THEN
        -- Delete old secret
        DELETE FROM vault.secrets WHERE id = v_vault_id;
    END IF;
    
    -- Create new secret
    v_secret_name := 'plaid_access_token_' || p_account_id::TEXT;
    
    SELECT vault.create_secret(
        p_new_access_token,
        v_secret_name,
        'Updated Plaid access token for account ' || p_account_id::TEXT
    ) INTO v_vault_id;
    
    -- Update financial_accounts table
    UPDATE financial_accounts 
    SET plaid_access_token_vault_id = v_vault_id
    WHERE id = p_account_id AND user_id = p_user_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to safely delete access token
CREATE OR REPLACE FUNCTION delete_plaid_access_token(
    p_user_id UUID,
    p_account_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_vault_id UUID;
BEGIN
    -- Get vault ID for the account
    SELECT plaid_access_token_vault_id INTO v_vault_id
    FROM financial_accounts
    WHERE id = p_account_id AND user_id = p_user_id;
    
    -- Delete from vault if exists
    IF v_vault_id IS NOT NULL THEN
        DELETE FROM vault.secrets WHERE id = v_vault_id;
        
        -- Clear vault reference
        UPDATE financial_accounts 
        SET plaid_access_token_vault_id = NULL
        WHERE id = p_account_id AND user_id = p_user_id;
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Note: Vault access security is handled by the SECURITY DEFINER functions above
-- which ensure users can only access tokens for their own financial accounts