'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  LayoutDashboard,
  CreditCard,
  FolderOpen,
  Settings,
  BarChart3,
  PiggyBank,
} from 'lucide-react';

const navigationItems = [
  {
    href: '/dashboard',
    label: 'Dashboard',
    icon: LayoutDashboard,
  },
  {
    href: '/dashboard/transactions',
    label: 'Transactions',
    icon: CreditCard,
  },
  {
    href: '/dashboard/budget',
    label: 'Budget',
    icon: PiggyBank,
  },
  {
    href: '/dashboard/categories',
    label: 'Categories',
    icon: FolderOpen,
  },
  {
    href: '/dashboard/rules',
    label: 'Rules',
    icon: Settings,
  },
  {
    href: '/dashboard/insights',
    label: 'Insights',
    icon: BarChart3,
  },
];

export default function DashboardNav() {
  const pathname = usePathname();

  return (
    <Card className='h-full w-64 flex flex-col'>
      <div className='p-6'>
        <h2 className='text-lg font-semibold text-foreground'>NAVsync.io</h2>
        <p className='text-sm text-muted-foreground'>Financial Dashboard</p>
      </div>

      <nav className='flex-1 px-4 pb-4'>
        <ul className='space-y-2'>
          {navigationItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <li key={item.href}>
                <Button
                  asChild
                  variant={isActive ? 'default' : 'ghost'}
                  className={cn(
                    'w-full justify-start gap-3 h-10',
                    isActive && 'bg-primary text-primary-foreground shadow-sm'
                  )}
                >
                  <Link href={item.href}>
                    <Icon className='h-4 w-4' />
                    {item.label}
                  </Link>
                </Button>
              </li>
            );
          })}
        </ul>
      </nav>
    </Card>
  );
}
