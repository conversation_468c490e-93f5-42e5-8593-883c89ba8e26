import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getAverageSpendingByCategory, BudgetError } from '@/lib/services/budgetService';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const monthsParam = searchParams.get('months');
    const months = monthsParam ? parseInt(monthsParam, 10) : 3;

    // Validate months parameter
    if (isNaN(months) || months < 1 || months > 12) {
      return NextResponse.json(
        { error: 'Invalid months parameter. Must be between 1 and 12.' },
        { status: 400 }
      );
    }

    // Get spending recommendations
    const recommendations = await getAverageSpendingByCategory(supabase, user.id, months);

    // Check if user has sufficient data
    if (recommendations.length === 0) {
      return NextResponse.json(
        {
          error: 'Insufficient data for recommendations',
          message:
            'You need at least 2 months of categorized transactions to generate budget recommendations. Please categorize your transactions and try again.',
          hasData: false,
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        recommendations,
        analysis_period_months: months,
        categories_analyzed: recommendations.length,
        total_recommended_budget: recommendations.reduce(
          (sum, rec) => sum + rec.recommended_budget,
          0
        ),
      },
    });
  } catch (error) {
    console.error('Error generating budget recommendations:', error);

    if (error instanceof BudgetError) {
      return NextResponse.json(
        {
          error: error.message,
          hasData: error.status === 400,
        },
        { status: error.status }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error while generating recommendations' },
      { status: 500 }
    );
  }
}
