'use client';

import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { X, Plus, Trash2 } from 'lucide-react';
import { Budget } from '@/lib/services/budgetService';
import { useCategories } from '@/lib/hooks/useCategories';
import { CategoryBudgetInput, CategoryBudgetInputValue } from './CategoryBudgetInput';
import { BudgetSummary } from './BudgetSummary';

const budgetItemSchema = z.object({
  id: z.string().optional(),
  categoryId: z.string().min(1, 'Category is required'),
  allocatedAmount: z.number().min(0.01, 'Amount must be greater than 0'),
  notes: z.string().optional(),
});

const budgetFormSchema = z.object({
  name: z.string().min(1, 'Budget name is required'),
  totalIncome: z.number().optional(),
  items: z.array(budgetItemSchema).min(1, 'At least one budget item is required'),
});

type BudgetFormData = z.infer<typeof budgetFormSchema>;

interface BudgetFormProps {
  budget?: Budget | null;
  month: string;
  onSubmit: () => void;
  onCancel: () => void;
}

export function BudgetForm({ budget, month, onSubmit, onCancel }: BudgetFormProps) {
  const { categories, isLoading: categoriesLoading } = useCategories();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<BudgetFormData>({
    resolver: zodResolver(budgetFormSchema),
    defaultValues: {
      name: budget?.name || `Budget for ${formatMonthYear(month)}`,
      totalIncome: budget?.total_income || undefined,
      items: budget?.items.map((item) => ({
        id: item.id,
        categoryId: item.category_id,
        allocatedAmount: item.allocated_amount,
        notes: item.notes || '',
      })) || [{ categoryId: '', allocatedAmount: 0, notes: '' }],
    },
  });

  const { append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  function formatMonthYear(monthStr: string) {
    const [year, month] = monthStr.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  }

  const watchedItems = form.watch('items');
  const watchedIncome = form.watch('totalIncome');

  const totalAllocated = watchedItems.reduce((sum, item) => {
    return sum + (item.allocatedAmount || 0);
  }, 0);

  const addBudgetItem = () => {
    append({ categoryId: '', allocatedAmount: 0, notes: '' });
  };

  const removeBudgetItem = (index: number) => {
    if (watchedItems.length > 1) {
      remove(index);
    }
  };

  const handleSubmit = async (data: BudgetFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const [year, monthNum] = month.split('-').map(Number);
      const startDate = new Date(year, monthNum - 1, 1).toISOString();
      const endDate = new Date(year, monthNum, 0, 23, 59, 59).toISOString();

      const payload = {
        name: data.name,
        startDate,
        endDate,
        totalIncome: data.totalIncome,
        items: data.items.map((item) => ({
          id: item.id,
          categoryId: item.categoryId,
          allocatedAmount: item.allocatedAmount,
          notes: item.notes || null,
        })),
      };

      const url = budget ? `/api/budgets/${budget.id}` : '/api/budgets';
      const method = budget ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save budget');
      }

      onSubmit();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save budget');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (categoriesLoading) {
    return (
      <div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='flex items-center justify-center py-8'>
            <LoadingSpinner />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4'>
      <Card className='w-full max-w-4xl max-h-[90vh] overflow-hidden'>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <div>
            <CardTitle>{budget ? 'Edit Budget' : 'Create Budget'}</CardTitle>
            <CardDescription>
              {budget ? 'Update your budget details' : 'Set up your monthly budget'}
            </CardDescription>
          </div>
          <Button variant='ghost' size='sm' onClick={onCancel}>
            <X className='h-4 w-4' />
          </Button>
        </CardHeader>

        <CardContent className='overflow-y-auto max-h-[calc(90vh-120px)]'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
              {error && (
                <div className='p-4 border border-destructive rounded-md bg-destructive/10'>
                  <p className='text-destructive text-sm'>{error}</p>
                </div>
              )}

              {/* Budget Name */}
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Budget Name</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter budget name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Total Income */}
              <FormField
                control={form.control}
                name='totalIncome'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Income (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type='number'
                        step='0.01'
                        placeholder='Enter your total income'
                        {...field}
                        onChange={(e) =>
                          field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Budget Summary */}
              <BudgetSummary income={watchedIncome || 0} allocated={totalAllocated} />

              {/* Budget Items */}
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium'>Budget Categories</h3>
                  <Button type='button' onClick={addBudgetItem} variant='outline' size='sm'>
                    <Plus className='h-4 w-4 mr-2' />
                    Add Category
                  </Button>
                </div>

                <div className='space-y-3'>
                  {watchedItems.map((item, index) => (
                    <div key={index} className='flex items-end gap-4 p-4 border rounded-lg'>
                      <div className='flex-1'>
                        <CategoryBudgetInput
                          categories={categories}
                          value={{
                            categoryId: item.categoryId,
                            allocatedAmount: item.allocatedAmount,
                            notes: item.notes || '',
                          }}
                          onChange={(value: CategoryBudgetInputValue) => {
                            form.setValue(`items.${index}.categoryId`, value.categoryId);
                            form.setValue(`items.${index}.allocatedAmount`, value.allocatedAmount);
                            form.setValue(`items.${index}.notes`, value.notes);
                          }}
                          error={{
                            categoryId: form.formState.errors.items?.[index]?.categoryId?.message,
                            allocatedAmount:
                              form.formState.errors.items?.[index]?.allocatedAmount?.message,
                          }}
                        />
                      </div>
                      {watchedItems.length > 1 && (
                        <Button
                          type='button'
                          variant='ghost'
                          size='sm'
                          onClick={() => removeBudgetItem(index)}
                          className='text-destructive hover:text-destructive'
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Form Actions */}
              <div className='flex justify-end gap-3 pt-6 border-t'>
                <Button type='button' variant='outline' onClick={onCancel}>
                  Cancel
                </Button>
                <Button type='submit' disabled={isSubmitting}>
                  {isSubmitting && <LoadingSpinner className='mr-2 h-4 w-4' />}
                  {budget ? 'Update Budget' : 'Create Budget'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
