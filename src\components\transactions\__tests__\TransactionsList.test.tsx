import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TransactionsList from '@/components/transactions/TransactionsList';
import { useIsDesktop } from '@/lib/hooks/useMediaQuery';
import { useTransactions } from '@/lib/hooks/useTransactions';
import ResponsiveTransactionView from '@/components/transactions/ResponsiveTransactionView';

// Mock the custom hooks
jest.mock('@/lib/hooks/useTransactions');
jest.mock('@/lib/hooks/useMediaQuery');

// Mock child components
jest.mock('@/components/transactions/ResponsiveTransactionView', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid='responsive-transaction-view'></div>),
}));

const mockUseTransactions = useTransactions as jest.Mock;
const mockUseIsDesktop = useIsDesktop as jest.Mock;
const mockResponsiveTransactionView = ResponsiveTransactionView as jest.Mock;

describe('TransactionsList', () => {
  beforeEach(() => {
    // Reset mocks before each test
    mockUseTransactions.mockClear();
    mockUseIsDesktop.mockClear();
    mockResponsiveTransactionView.mockClear();
  });

  test('renders ResponsiveTransactionView with correct props', async () => {
    const mockTransactions = [{ id: '1', name: 'Test Transaction' }];
    const mockPagination = { currentPage: 1, totalPages: 1, totalCount: 1 };
    const mockError = null;
    const mockIsLoading = false;

    mockUseIsDesktop.mockReturnValue(true); // Simulate desktop view
    mockUseTransactions.mockReturnValue({
      transactions: mockTransactions,
      pagination: mockPagination,
      isLoading: mockIsLoading,
      error: mockError,
      handlePageChange: jest.fn(),
      retryFetch: jest.fn(),
    });

    render(<TransactionsList />);

    await waitFor(() => {
      expect(mockResponsiveTransactionView).toHaveBeenCalled();
      expect(mockResponsiveTransactionView.mock.calls[0][0]).toEqual(
        expect.objectContaining({
          transactions: mockTransactions,
          isLoading: mockIsLoading,
          error: mockError,
          selectedTransactionIds: [],
          onTransactionClick: expect.any(Function),
          onToggleSelected: expect.any(Function),
          onBulkCategorize: expect.any(Function),
        })
      );
    });
  });
});
